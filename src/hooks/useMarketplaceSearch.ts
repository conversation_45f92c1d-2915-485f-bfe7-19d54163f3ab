'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import { api } from '@/lib/api';
import { MCP, Agent, Workflow } from '@/lib/api/types/marketplace-items';

interface UseMarketplaceSearchProps {
  defaultTab?: string;
  defaultCategory?: string;
}

interface SearchParams {
  search?: string;
  tags?: string;
  category?: string;
  page?: number;
  page_size?: number;
}

export function useMarketplaceSearch({
  defaultTab = 'all',
  defaultCategory = 'all'
}: UseMarketplaceSearchProps = {}) {
  // State for search parameters
  const [searchQuery, setSearchQuery] = useState('');
  const [tagsFilter, setTagsFilter] = useState('');
  const [currentTab, setCurrentTab] = useState(defaultTab);
  const [currentCategory, setCurrentCategory] = useState(defaultCategory);

  // State for API data
  const [items, setItems] = useState<(MCP | Agent | Workflow)[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Next.js navigation hooks
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize from URL parameters
  useEffect(() => {
    if (!searchParams) return;

    const tab = searchParams.get('tab') || defaultTab;
    const category = searchParams.get('category') || defaultCategory;
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';

    setCurrentTab(tab);
    setCurrentCategory(category);
    setSearchQuery(search);
    setTagsFilter(tags);

    // Fetch data with initial parameters
    fetchData(tab, search, tags, category);
  }, [searchParams, defaultTab, defaultCategory]);

  // Function to fetch data based on search parameters
  const fetchData = async (
    tab: string,
    search?: string,
    tags?: string,
    category?: string
  ) => {
    setLoading(true);
    setError(null);

    // Prepare API parameters
    const apiParams: SearchParams = {
      page: 1,
      page_size: 20,
    };

    // Only add non-empty parameters
    if (search) apiParams.search = search;
    if (tags) apiParams.tags = tags;
    if (category && category !== 'all') apiParams.category = category;

    console.log(`Fetching ${tab} with params:`, apiParams);

    try {
      let response;

      // Fetch data based on the current tab
      switch (tab) {
        case 'mcps':
          response = await api.mcp.getMCPs(apiParams);
          console.log('MCP response:', response);
          if (response.data) {
            // Handle different response structures
            if (response.data.data && Array.isArray(response.data.data)) {
              // New format with data array
              setItems(response.data.data);
            }else {
              console.error('Unexpected MCP response format:', response.data);
              setItems([]);
            }
          } else {
            setItems([]);
          }
          break;

        case 'agents':
          response = await api.agent.getAgents(apiParams);
          console.log('Agent response:', response);
          if (response.data) {
            // Handle different response structures
            if (response.data.data && Array.isArray(response.data.data)) {
              // New format with data array
              setItems(response.data.data);
            }else {
              console.error('Unexpected Agent response format:', response.data);
              setItems([]);
            }
          } else {
            setItems([]);
          }
          break;

        case 'workflows':
          response = await api.workflow.getWorkflows(apiParams);
          console.log('Workflow response:', response);
          if (response.data) {
            // Handle different response structures
            if (response.data.data && Array.isArray(response.data.data)) {
              // New format with data array
              setItems(response.data.data);
            }
          } else {
            setItems([]);
          }
          break;

        case 'all':
        default:
          response = await api.combinedMarketplace.getCombinedItems({
            ...apiParams,
            category_type: undefined // Don't filter by type when on "all" tab
          });
          console.log('Combined response:', response);
          if (response.data) {
            // Handle different response structures
            if (response.data.data && Array.isArray(response.data.data)) {
              // New format with data array
              setItems(response.data.data);
            }
          } else {
            setItems([]);
          }
          break;
      }
    } catch (err) {
      console.error(`Error fetching ${tab}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to fetch ${tab}`));
      setItems([]);
    } finally {
      setLoading(false);
    }
  };

  // Function to handle search
  const handleSearch = (query: string, tags?: string) => {
    if (!searchParams) return;

    // Update URL with search query and tags
    const params = new URLSearchParams(searchParams.toString());

    if (query) {
      params.set('search', query);
    } else {
      params.delete('search');
    }

    if (tags) {
      params.set('tags', tags);
    } else {
      params.delete('tags');
    }

    // Update URL without full navigation
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });

    // Update state
    setSearchQuery(query);
    setTagsFilter(tags || '');

    // Fetch data with new parameters
    fetchData(currentTab, query, tags, currentCategory);
  };

  // Function to handle tab change
  const handleTabChange = (tab: string) => {
    if (!searchParams) return;

    // Update URL with new tab
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tab);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });

    // Update state
    setCurrentTab(tab);

    // Fetch data with new tab
    fetchData(tab, searchQuery, tagsFilter, currentCategory);
  };

  // Function to handle category change
  const handleCategoryChange = (category: string) => {
    if (!searchParams) return;

    // Update URL with new category
    const params = new URLSearchParams(searchParams.toString());
    params.set('category', category);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });

    // Update state
    setCurrentCategory(category);

    // Fetch data with new category
    fetchData(currentTab, searchQuery, tagsFilter, category);
  };

  // Function to clear all filters
  const clearFilters = () => {
    if (!searchParams) return;

    // Update URL by removing search and tags parameters
    const params = new URLSearchParams(searchParams.toString());
    params.delete('search');
    params.delete('tags');
    params.set('category', 'all');
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });

    // Update state
    setSearchQuery('');
    setTagsFilter('');
    setCurrentCategory('all');

    // Fetch data with cleared filters
    fetchData(currentTab, '', '', 'all');
  };

  return {
    // State
    items,
    loading,
    error,
    searchQuery,
    tagsFilter,
    currentTab,
    currentCategory,

    // Actions
    handleSearch,
    handleTabChange,
    handleCategoryChange,
    clearFilters,

    // Direct access to fetch function for manual refetching
    refetch: fetchData
  };
}
