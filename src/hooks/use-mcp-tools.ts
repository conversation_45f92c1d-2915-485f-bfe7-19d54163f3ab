'use client';

import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { MCPToolExecutionRequest } from '@/lib/api/services/mcp-tools.service';

/**
 * Hook for executing MCP tools
 * Uses mutation since tool execution is a side effect operation
 */
export function useMCPToolExecution() {
  return useMutation({
    mutationFn: async (request: MCPToolExecutionRequest) => {
      const response = await api.mcpTools.executeTool(request);
      return response.data;
    },
    onSuccess: (data, variables) => {
      console.log(`Successfully executed tool ${variables.tool_name} for MCP ${variables.mcp_id}:`, data);
    },
    onError: (error, variables) => {
      console.error(`Failed to execute tool ${variables.tool_name} for MCP ${variables.mcp_id}:`, error);
    },
  });
}
