'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { MarketplaceFilterParams } from '@/lib/api/types/marketplace-items';

/**
 * Hook for fetching combined marketplace items (MCPs, Agents, and Workflows)
 * @param params Optional filter parameters
 * @returns Query result with combined marketplace items
 */
export function useCombinedMarketplace(params?: MarketplaceFilterParams) {
  return useQuery({
    queryKey: ['combined-marketplace', params],
    queryFn: async () => {
      try {
        const response = await api.combinedMarketplace.getCombinedItems(params);
        console.log("combined items: ", response);
        return response.data;
      } catch (error) {
        console.error('Error fetching combined marketplace items:', error);
        throw error;
      }
    },
  });
}

/**
 * Hook for fetching a single marketplace item by ID and type
 * @param id Item ID
 * @param type Item type (MCP, AGENT, or WORKFLOW)
 * @returns Query result with the item
 */
export function useCombinedMarketplaceItem(id: string, type: 'MCP' | 'AGENT' | 'WORKFLOW') {
  return useQuery({
    queryKey: ['combined-marketplace-item', id, type],
    queryFn: async () => {
      try {
        const response = await api.combinedMarketplace.getCombinedItemById(id, type);
        return response.data;
      } catch (error) {
        console.error(`Error fetching item with ID ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id && !!type,
  });
}
