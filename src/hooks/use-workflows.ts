'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { MarketplaceFilterParams } from '@/lib/api/types/marketplace-items';

/**
 * Hook to fetch all Workflows with optional filtering
 */
export function useWorkflows(params?: MarketplaceFilterParams) {
  return useQuery({
    queryKey: ['workflows', params],
    queryFn: async () => {
      const response = await api.workflow.getWorkflows(params);
      // Transform the response to match the expected format in components
      return {
        templates: response.data.data,
        total: response.data.metadata.total,
        page: response.data.metadata.page,
        total_pages: response.data.metadata.total_pages
      };
    },
  });
}

/**
 * Hook to fetch a single Workflow by ID
 * @param id Workflow ID
 * @param userId Optional user ID to check if the item is already added
 */
export function useWorkflow(id: string, userId?: string) {
  return useQuery({
    queryKey: ['workflow', id, userId], // Include userId in the query key to refetch when it changes
    queryFn: async () => {
      const response = await api.workflow.getWorkflowById(id, userId);
      return response.data;
    },
    enabled: !!id, // Only run the query if an ID is provided
  });
}
