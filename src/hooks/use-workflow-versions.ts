'use client';

import { api, ListWorkflowVersionsResponse } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';

interface UseWorkflowVersionsParams {
  workflowId: string;
  page?: number;
  pageSize?: number;
  enabled?: boolean;
}

/**
 * Hook to fetch versions for a specific workflow
 * @param params Parameters for fetching workflow versions
 * @returns Query result with workflow versions data
 */
export function useWorkflowVersions({
  workflowId,
  page = 1,
  pageSize = 10,
  enabled = true
}: UseWorkflowVersionsParams) {
  return useQuery<ListWorkflowVersionsResponse, Error>({
    queryKey: ['workflow-versions', workflowId, page, pageSize],
    queryFn: async () => {
      if (!workflowId) {
        throw new Error('No workflow ID provided');
      }

      const response = await api.workflow.getWorkflowVersions(workflowId, {
        page,
        page_size: pageSize
      });

      return response.data;
    },
    enabled: enabled && !!workflowId,
  });
}