'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { MarketplaceFilterParams } from '@/lib/api/types/marketplace-items';

/**
 * Hook to fetch all MCPs with optional filtering
 */
export function useMCPs(params?: MarketplaceFilterParams) {
  return useQuery({
    queryKey: ['mcps', params],
    queryFn: async () => {
      const response = await api.mcp.getMCPs(params);
      return response.data;
    },
  });
}

/**
 * Hook to fetch a single MCP by ID
 * @param id MCP ID
 * @param userId Optional user ID to check if the item is already added
 */
export function useMCP(id: string, userId?: string) {
  return useQuery({
    queryKey: ['mcp', id, userId], // Include userId in the query key to refetch when it changes
    queryFn: async () => {
      const response = await api.mcp.getMCPById(id, userId);
      return response.data;
    },
    enabled: !!id, // Only run the query if an ID is provided
  });
}
