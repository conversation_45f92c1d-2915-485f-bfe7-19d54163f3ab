'use client';

import { useEffect, useRef, useState } from 'react';

// Global registry to track active streams
const activeStreams = new Map<string, EventSource>();

export interface SSEMessage {
  data: any;
  event?: string;
  id?: string;
}

export interface UseSSEStreamOptions {
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Event) => void;
  onOpen?: (event: Event) => void;
  onClose?: (event: Event) => void; // For server-sent 'close' type events or explicit client close
}

/**
 * Hook for Server-Sent Events streaming
 * @param requestId The request ID to stream from
 * @param options Event handlers and configuration
 */
export function useSSEStream(requestId: string | null, options: UseSSEStreamOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<SSEMessage[]>([]);
  const eventSourceRef = useRef<EventSource | null>(null);
  const initializedRequestIdRef = useRef<string | null>(null); // Tracks the requestId this hook instance initialized

  const callbacksRef = useRef(options);
  callbacksRef.current = options;

  useEffect(() => {
    const effectRequestId = requestId; // Capture requestId at the time of effect execution

    const attachListeners = (es: EventSource, currentReqId: string) => {
      es.onopen = (event) => {
        console.log(`SSE stream opened for ${currentReqId}`);
        setIsConnected(true);
        setError(null);
        callbacksRef.current.onOpen?.(event);
      };

      es.onmessage = (event) => {
        try {
          const parsedData = JSON.parse(event.data);
          console.log(`SSE message for ${currentReqId}:`, parsedData);

          if (parsedData.message === "Stream connected" && parsedData.id) {
            console.log(`SSE 'Stream connected' event for ${currentReqId}, ignoring for data processing.`);
            return;
          }

          if (parsedData.mcp_status === "success") {
            const sseMessage: SSEMessage = {
              data: parsedData.result,
              id: event.lastEventId || parsedData.request_id || undefined,
              event: event.type,
            };
            setMessages(prev => [...prev, sseMessage]);
            callbacksRef.current.onMessage?.(sseMessage);

            console.log(`SSE success, closing stream for ${currentReqId}`);
            es.close();
            activeStreams.delete(currentReqId);
            setIsConnected(false);
            // initializedRequestIdRef and eventSourceRef remain to mark this ID as processed by this instance
            return;
          }
          // Log other non-terminal messages
          console.log(`Intermediate SSE message for ${currentReqId}:`, parsedData);

        } catch (parseError) {
          console.error(`Error parsing SSE message for ${currentReqId}:`, parseError, "Raw data:", event.data);
          setError('Error parsing message data');
          if (callbacksRef.current.onError) {
            const errorEvent = new CustomEvent('parseerror', { detail: { originalError: parseError, rawData: event.data } });
            callbacksRef.current.onError(errorEvent as Event);
          }
        }
      };

      es.onerror = (event) => {
        console.error(`SSE error for ${currentReqId}:`, event);
        setIsConnected(false);
        setError('SSE connection error');
        callbacksRef.current.onError?.(event);
        // Browser usually closes the connection on error.
        if (es.readyState === EventSource.CLOSED) {
          activeStreams.delete(currentReqId);
        }
        // initializedRequestIdRef and eventSourceRef remain
      };

      // For server-sent custom 'close' events, if any.
      // Note: `eventSource.close()` on the client does not trigger this.
      es.addEventListener('close', (event) => {
        console.log(`SSE 'close' event received from server for ${currentReqId}`);
        setIsConnected(false);
        callbacksRef.current.onClose?.(event);
        activeStreams.delete(currentReqId);
        // initializedRequestIdRef and eventSourceRef remain
      });
    };
    
    if (!effectRequestId) {
      console.log('No requestId, cleaning up existing stream if any.');
      if (eventSourceRef.current && initializedRequestIdRef.current) {
        console.log(`Cleaning up stream for ${initializedRequestIdRef.current} (due to null effectRequestId)`);
        eventSourceRef.current.close();
        activeStreams.delete(initializedRequestIdRef.current);
        eventSourceRef.current = null;
        initializedRequestIdRef.current = null;
      }
      setIsConnected(false);
      setMessages([]); // Clear messages when no request ID
      setError(null);
      return;
    }

    // Case 1: This hook instance already initialized for THIS effectRequestId
    if (initializedRequestIdRef.current === effectRequestId && eventSourceRef.current) {
      console.log(`SSE stream for ${effectRequestId} already managed by this instance. State: ${eventSourceRef.current.readyState}`);
      const currentOpenState = eventSourceRef.current.readyState === EventSource.OPEN;
      if (isConnected !== currentOpenState) {
        setIsConnected(currentOpenState);
      }
      // Re-attach listeners to ensure callbacks are current (if options changed)
      // This is a simplification; ideally, listeners are managed more granularly if they can change.
      // However, callbacksRef.current handles the callback changes.
      return; // Do not re-initialize
    }

    // Case 2: This instance was managing a DIFFERENT stream; clean it up before proceeding.
    if (initializedRequestIdRef.current && initializedRequestIdRef.current !== effectRequestId && eventSourceRef.current) {
      console.log(`Switching from ${initializedRequestIdRef.current} to ${effectRequestId}. Closing old stream.`);
      eventSourceRef.current.close();
      activeStreams.delete(initializedRequestIdRef.current);
      // eventSourceRef.current and initializedRequestIdRef.current will be reset below
    }
    
    // Reset messages and error for the new/adopted stream
    setMessages([]);
    setError(null);

    // Case 3: Check global registry for an existing, open stream for effectRequestId
    const existingGlobalStream = activeStreams.get(effectRequestId);
    if (existingGlobalStream && existingGlobalStream.readyState !== EventSource.CLOSED) {
      console.log(`Adopting existing global SSE stream for ${effectRequestId}. State: ${existingGlobalStream.readyState}`);
      eventSourceRef.current = existingGlobalStream;
      initializedRequestIdRef.current = effectRequestId;
      attachListeners(eventSourceRef.current, effectRequestId); // Attach this instance's listeners
      setIsConnected(existingGlobalStream.readyState === EventSource.OPEN);
      return;
    } else if (existingGlobalStream && existingGlobalStream.readyState === EventSource.CLOSED) {
      console.log(`Found stale closed global stream for ${effectRequestId}. Removing.`);
      activeStreams.delete(effectRequestId); // Remove stale entry
    }

    // Case 4: No suitable stream found, create a new one.
    const baseUrl = process.env.NEXT_PUBLIC_RUH_EXECUTION_API_URL;
    if (!baseUrl) {
      setError('SSE base URL not configured');
      console.error('SSE base URL not configured');
      return;
    }
    const url = `${baseUrl}/mcp-execute/stream/${effectRequestId}`;
    console.log(`Initializing new SSE stream for ${effectRequestId} at ${url}`);

    try {
      const newEventSource = new EventSource(url);
      eventSourceRef.current = newEventSource;
      initializedRequestIdRef.current = effectRequestId;
      activeStreams.set(effectRequestId, newEventSource);
      attachListeners(newEventSource, effectRequestId);
      // onopen will set isConnected
    } catch (err) {
      console.error(`Failed to establish SSE connection for ${effectRequestId}:`, err);
      setError('Failed to establish SSE connection');
      eventSourceRef.current = null;
      initializedRequestIdRef.current = null; // Clear if creation failed
    }

    return () => {
      // Cleanup for the requestId this effect instance was specifically set up for
      console.log(`Cleanup running for useSSEStream effect (effectRequestId: ${effectRequestId})`);
      if (initializedRequestIdRef.current === effectRequestId && eventSourceRef.current) {
        // This instance created or adopted this specific stream
        console.log(`Performing specific cleanup for stream: ${effectRequestId}`);
        // Check if this stream is still the one in activeStreams before deleting,
        // another instance might have taken over if this was a shared stream scenario (though less likely with current ToolSSEStream design)
        if (activeStreams.get(effectRequestId) === eventSourceRef.current) {
          activeStreams.delete(effectRequestId);
        }
        if (eventSourceRef.current.readyState !== EventSource.CLOSED) {
            eventSourceRef.current.close();
        }
        eventSourceRef.current = null;
        initializedRequestIdRef.current = null; // This instance no longer "owns" a stream for this ID
      } else {
        console.log(`Cleanup for ${effectRequestId}: No specific stream owned by this instance or ID mismatch (current owned: ${initializedRequestIdRef.current})`);
      }
      // Avoid setting isConnected to false globally here if another instance might be active
      // setIsConnected(false);
    };
  }, [requestId]); // Effect dependency

  const closeConnection = () => {
    if (eventSourceRef.current && initializedRequestIdRef.current) {
      console.log(`Manually closing SSE stream for ${initializedRequestIdRef.current}`);
      eventSourceRef.current.close();
      activeStreams.delete(initializedRequestIdRef.current);
      setIsConnected(false);
      // Keep refs to indicate it was processed, even if manually closed
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return {
    isConnected,
    error,
    messages,
    closeConnection,
    clearMessages,
  };
}
