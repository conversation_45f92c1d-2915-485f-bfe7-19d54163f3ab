'use client';

import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { WorkflowData } from '@/lib/api/types/workflow-data';

/**
 * Hook to fetch workflow data from a URL
 * @param url The URL to fetch workflow data from
 * @returns Query result with workflow data
 */
export function useWorkflowData(url?: string) {
  return useQuery({
    queryKey: ['workflow-data', url],
    queryFn: async (): Promise<WorkflowData> => {
      if (!url) {
        throw new Error('No workflow URL provided');
      }

      try {
        // Check if the URL is actually a JSON string (starts with '{')
        if (url.startsWith('{')) {
          console.log('URL appears to be a JSON string, parsing directly');
          try {
            const parsedData = JSON.parse(url) as WorkflowData;
            console.log('Parsed workflow data:', parsedData);
            return parsedData;
          } catch (parseError) {
            console.error('Error parsing workflow JSON:', parseError);
            throw new Error('Invalid workflow JSON format');
          }
        }

        // Check if the URL is a JSON string but doesn't start with '{'
        // (e.g., it might have whitespace or be escaped)
        if (url.includes('{') && url.includes('}')) {
          try {
            // Try to extract JSON from the string
            // Using a workaround for the 's' flag which is not available in ES2017
            const jsonMatch = url.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              console.log('Extracted JSON string:', jsonStr);
              const parsedData = JSON.parse(jsonStr) as WorkflowData;
              console.log('Parsed workflow data from extracted JSON:', parsedData);
              return parsedData;
            }
          } catch (parseError) {
            console.error('Error parsing extracted JSON:', parseError);
            // Continue to URL fetch if this fails
          }
        }

        console.log('Fetching workflow data from URL:', url);

        // Use axios directly (not axiosClient) to fetch the data
        const response = await axios.get(url, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        console.log('Workflow data response:', response.data);

        // Check if the response is a string that might contain JSON
        if (typeof response.data === 'string' && response.data.includes('{') && response.data.includes('}')) {
          try {
            // Try to extract JSON from the string
            // Using a workaround for the 's' flag which is not available in ES2017
            const jsonMatch = response.data.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              console.log('Extracted JSON string from response:', jsonStr);
              const parsedData = JSON.parse(jsonStr) as WorkflowData;
              return parsedData;
            }
          } catch (parseError) {
            console.error('Error parsing JSON from response string:', parseError);
          }
        }

        return response.data;
      } catch (error) {
        console.error('Error fetching workflow data:', error);
        throw error;
      }
    },
    enabled: !!url, // Only run the query if a URL is provided
  });
}
