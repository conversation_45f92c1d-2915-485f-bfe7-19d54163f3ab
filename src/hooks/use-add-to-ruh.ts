'use client';

import { useAuth } from '@/hooks/use-auth';
import { api } from '@/lib/api';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';

interface UseAddToRuhOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  refetch?: () => void;
}

interface AddToRuhResponse {
  success: boolean;
  message: string;
  item_id: string;
  item_type: string;
  use_count: number;
}

/**
 * Hook for adding an item to RUH
 * Handles authentication check and API call
 */
export function useAddToRuh(options?: UseAddToRuhOptions) {
  const { isAuthenticated } = useAuth();
  const [isAddingToRuh, setIsAddingToRuh] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [successData, setSuccessData] = useState<{
    itemId: string;
    itemType: string;
  } | null>(null);

  // Mutation for adding an item to RUH
  const mutation = useMutation<AddToRuhResponse, Error, { itemId: string; itemType: 'AGENT' | 'WORKFLOW' | 'MCP' }>({
    mutationFn: async ({ itemId, itemType }) => {
      const response = await api.marketplace.useItem(itemId, itemType);
      // The response is wrapped in an ApiResponse object, so we need to extract the data
      return response.data;
    },
    onSuccess: (data: AddToRuhResponse, variables) => {
      // Store the success data
      setSuccessData({
        itemId: data?.item_id || variables.itemId,
        itemType: data?.item_type || variables.itemType
      });

      // Show success dialog instead of toast
      setShowSuccessDialog(true);

      // Refetch the item details to update the button state
      options?.refetch?.();

      // Still call the onSuccess callback if provided
      options?.onSuccess?.();
    },
    onError: (error: Error) => {
      console.error('Error adding to RUH:', error);
      toast.error('Failed to add to RUH', {
        description: error.message || 'An error occurred while adding the item to RUH.',
      });
      options?.onError?.(error);
    },
    onSettled: () => {
      setIsAddingToRuh(false);
    },
  });

  /**
   * Add an item to RUH
   * Checks authentication first and shows login dialog if not authenticated
   */
  const addToRuh = async (itemId: string, itemType: 'AGENT' | 'WORKFLOW' | 'MCP') => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      // Show login dialog
      setShowLoginDialog(true);
      return;
    }

    // Set loading state
    setIsAddingToRuh(true);

    // Call the mutation
    mutation.mutate({ itemId, itemType });
  };

  /**
   * Close the login dialog
   */
  const closeLoginDialog = () => {
    setShowLoginDialog(false);
  };

  /**
   * Close the success dialog
   */
  const closeSuccessDialog = () => {
    setShowSuccessDialog(false);
  };

  return {
    addToRuh,
    isAddingToRuh,
    error: mutation.error,
    showLoginDialog,
    closeLoginDialog,
    showSuccessDialog,
    closeSuccessDialog,
    successData,
  };
}
