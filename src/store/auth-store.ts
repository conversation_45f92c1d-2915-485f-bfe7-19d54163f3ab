import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { cookieUtils } from '@/lib/cookie';
import { UserDetails } from '@/services/user-service';

interface AuthState {
  user: UserDetails | null;
  token: string | null | undefined;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: UserDetails | null) => void;
  setToken: (token: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: UserDetails, token: string) => void;
  logout: () => void;
}

// Get token from cookies if available (client-side only)
const getInitialToken = () => {
  if (typeof window !== 'undefined') {
    return cookieUtils.getAccessToken();
  }
  return null;
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: getInitialToken(),
      isAuthenticated: !!getInitialToken(),
      isLoading: false,
      error: null,

      // Actions
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token }),
      setIsLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),

      login: (user, token) => set({
        user,
        token,
        isAuthenticated: true,
        error: null,
      }),

      logout: () => set({
        user: null,
        token: null,
        isAuthenticated: false,
        error: null,
      }),
    }),
    {
      name: 'auth-storage',
      // Only persist these fields
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
