/* Custom animations for the hero section */

/* Blob animations */
@keyframes blob-move-1 {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  33% {
    transform: translate(30px, -20px) scale(1.1) rotate(5deg);
  }
  66% {
    transform: translate(-20px, 10px) scale(0.9) rotate(-5deg);
  }
  100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

@keyframes blob-move-2 {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  33% {
    transform: translate(-30px, 20px) scale(1.15) rotate(-8deg);
  }
  66% {
    transform: translate(20px, -15px) scale(0.95) rotate(8deg);
  }
  100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

@keyframes blob-move-3 {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(15px, -25px) scale(1.05) rotate(3deg);
  }
  50% {
    transform: translate(-10px, -10px) scale(0.95) rotate(-3deg);
  }
  75% {
    transform: translate(-20px, 15px) scale(1.1) rotate(5deg);
  }
  100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

/* Text animations */
@keyframes text-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float-up-down {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(174, 0, 208, 0.3), 0 0 15px rgba(174, 0, 208, 0.2);
  }
  50% {
    text-shadow: 0 0 10px rgba(174, 0, 208, 0.5), 0 0 25px rgba(174, 0, 208, 0.3);
  }
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: rgba(174, 0, 208, 0.7) }
}

.typing-animation {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  border-right: 3px solid rgba(174, 0, 208, 0.7);
  animation:
    typing 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

/* Button animations */
@keyframes pulse-button {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(174, 0, 208, 0);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 0 10px rgba(174, 0, 208, 0.3);
  }
}

/* Apply animations */
.blob-animation-1 {
  animation: blob-move-1 15s ease-in-out infinite;
}

.blob-animation-2 {
  animation: blob-move-2 18s ease-in-out infinite;
}

.blob-animation-3 {
  animation: blob-move-3 20s ease-in-out infinite;
}

.text-shimmer {
  background: linear-gradient(90deg,
    rgba(174, 0, 208, 0.7) 0%,
    rgba(199, 46, 224, 0.9) 25%,
    rgba(174, 0, 208, 0.7) 50%,
    rgba(199, 46, 224, 0.9) 75%,
    rgba(174, 0, 208, 0.7) 100%);
  background-size: 200% auto;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  animation: text-shimmer 8s linear infinite;
}

.float-animation {
  animation: float-up-down 4s ease-in-out infinite;
}

.glow-animation {
  animation: glow 3s ease-in-out infinite;
}

.pulse-button {
  animation: pulse-button 3s ease-in-out infinite;
}

/* Button hover animations */
.button-hover {
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.button-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.button-hover:active {
  transform: translateY(1px);
}

.button-hover::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.button-hover:hover::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

/* Agent mini card animations */
@keyframes agent-float {
  0% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
  25% {
    transform: translateY(-12px) rotate(calc(var(--rotation, 0deg) + 2deg));
  }
  50% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
  75% {
    transform: translateY(8px) rotate(calc(var(--rotation, 0deg) - 2deg));
  }
  100% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
}

@keyframes agent-appear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.7) rotate(var(--rotation, 0deg));
  }
  70% {
    opacity: 1;
    transform: translateY(-10px) scale(1.05) rotate(calc(var(--rotation, 0deg) + 3deg));
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotate(var(--rotation, 0deg));
  }
}

@keyframes agent-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

.agent-float-animation {
  animation: agent-float var(--float-duration, 6s) ease-in-out infinite;
  animation-delay: var(--delay, 0ms);
}

.agent-appear-animation {
  animation: agent-appear 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  animation-delay: var(--delay, 0ms);
  opacity: 0;
}

.agent-pulse-animation {
  animation: agent-pulse var(--pulse-duration, 3s) ease-in-out infinite;
  animation-delay: calc(var(--delay, 0ms) + 1000ms);
}

/* Animations for marketplace cards */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-10px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0) rotate(var(--rotation, 0deg));
  }
  25% {
    transform: translateY(-8px) rotate(calc(var(--rotation, 0deg) + 2deg));
  }
  75% {
    transform: translateY(5px) rotate(calc(var(--rotation, 0deg) - 2deg));
  }
}

.animate-bounce-slow {
  animation: bounce-slow 3s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

.animate-float-slow {
  animation: float-slow 4s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

/* Add a subtle pulse animation */
@keyframes pulse-slow {
  0%, 100% {
    transform: scale(1) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: scale(1.03) rotate(var(--rotation, 0deg));
  }
}

.animate-pulse-slow {
  animation: pulse-slow 5s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}