"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { getRuhUrl } from "@/lib/helpers";
import { CheckCircle, ExternalLink } from "lucide-react";

interface SuccessDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description?: string;
  itemId?: string;
  itemType?: string;
}

export function SuccessDialog({
  isOpen,
  onOpenChange,
  title = "Added Successfully",
  description = "The item has been added to your RUH account.",
  itemId,
  itemType,
}: SuccessDialogProps) {
  const handleViewInRuh = () => {
    const ruhUrl = getRuhUrl();
    const dashboardUrl = `${ruhUrl}/dashboard/employee/${itemId}`;
    window.open(dashboardUrl, "_blank");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex justify-center mb-2">
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
          <DialogTitle className="text-center">{title}</DialogTitle>
          <DialogDescription className="text-center">
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground text-center mb-4">
            You can now access this {itemType?.toLowerCase()} in your RUH dashboard.
          </p>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className={itemType?.toUpperCase() === "AGENT" ? "sm:flex-1" : "w-full"}
          >
            Continue Exploring
          </Button>
          {itemType?.toUpperCase() === "AGENT" && (
            <Button
              onClick={handleViewInRuh}
              className="sm:flex-1"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View in RUH
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
