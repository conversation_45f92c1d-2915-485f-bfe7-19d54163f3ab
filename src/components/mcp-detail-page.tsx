"use client";

import { BaseDetailPage } from "@/components/base-detail-page";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { MarketplaceItem } from "@/data/marketplace-items";
import { redirectToDeveloperPlatform } from "@/lib/helpers";
import { ArrowRight, ChevronDown, ChevronUp, Play, Settings, AlertTriangle, Zap, CheckCircle, Clock } from "lucide-react";
import React, { useState, useCallback, useEffect } from "react";
import { McpConnectionSheet, EnvConfigKey } from "./mcp-connection-sheet";
import { useAuth } from "@/hooks/use-auth";
import { LoginDialog } from "./login-dialog";
import { api } from "@/lib/api";
import type { CreateContainerRequest } from "@/lib/api/services/mcp.service";
import { useMCPToolExecution } from "@/hooks/use-mcp-tools";
import { useSSEStream, type SSEMessage } from "@/hooks/use-sse-stream";

interface MCPDetailPageProps {
  item: MarketplaceItem;
  relatedItems?: MarketplaceItem[];
  refetch?: () => void;
}

interface ToolParameter {
  type: string;
  description: string;
}

interface SchemaProperties {
  [key: string]: ToolParameter;
}

interface ToolSchema {
  type: string;
  properties: SchemaProperties;
  required?: string[];
}

interface Tool {
  name: string;
  description: string;
  input_schema?: ToolSchema;
  output_schema?: ToolSchema;
}

// Define ToolSSEStream props
interface ToolSSEStreamProps {
  toolName: string;
  requestId: string | null; // requestId can be null initially
  onMessageReceived: (toolName: string, message: SSEMessage) => void;
  onStreamError: (toolName: string, error: Event) => void;
  onStreamClose: (toolName: string) => void;
}

// Define ToolSSEStream outside MCPDetailPage for stability
const ToolSSEStream = React.memo(({
  toolName,
  requestId,
  onMessageReceived,
  onStreamError,
  onStreamClose,
}: ToolSSEStreamProps) => {
  const handleMessage = useCallback((message: SSEMessage) => {
    onMessageReceived(toolName, message);
  }, [toolName, onMessageReceived]);

  const handleError = useCallback((error: Event) => {
    onStreamError(toolName, error);
  }, [toolName, onStreamError]);

  const handleClose = useCallback(() => { // This is for the hook's onClose, not a server 'close' event
    onStreamClose(toolName);
  }, [toolName, onStreamClose]);

  // Pass null if requestId is not yet available for a tool
  useSSEStream(requestId, {
    onMessage: handleMessage,
    onError: handleError,
    onClose: handleClose, // Hook's onClose for when stream terminates
  });

  return null; // This component doesn't render anything itself
});
ToolSSEStream.displayName = 'ToolSSEStream';


export function MCPDetailPage({ item, relatedItems = [], refetch }: MCPDetailPageProps) {
  const [installMethod, setInstallMethod] = useState<'json' | 'url'>('json');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [expandedTool, setExpandedTool] = useState<string | null>(null);
  const [expandedOptionalParams, setExpandedOptionalParams] = useState<string | null>(null);

  const [isMcpConnected, setIsMcpConnected] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [isLoadingEnvDetails, setIsLoadingEnvDetails] = useState(false);
  const [envConfigKeys, setEnvConfigKeys] = useState<EnvConfigKey[]>([]);
  const [userEnvValues, setUserEnvValues] = useState<Record<string, string>>({});

  const [toolParameters, setToolParameters] = useState<Record<string, Record<string, string>>>({});
  const [toolResults, setToolResults] = useState<Record<string, any>>({});
  const [executingTools, setExecutingTools] = useState<Set<string>>(new Set());
  const [activeStreams, setActiveStreams] = useState<Record<string, string | null>>({}); // Store requestId or null

  const { isAuthenticated, user } = useAuth();
  const toolExecution = useMCPToolExecution();
  const tools: Tool[] = item.rawData?.mcp_tools_config?.tools || [];
  const initialEnvConfigKeys: EnvConfigKey[] = item.rawData?.env_keys || [];

  // Callbacks for ToolSSEStream, defined in MCPDetailPage
  const handleSSEMessageReceived = useCallback((toolName: string, message: SSEMessage) => {
    console.log(`SSE Message in MCPDetailPage for ${toolName}:`, message);
    if (message && message.data && Array.isArray(message.data) && message.data.length > 0) {
      const toolOutput = message.data;
      setToolResults(prev => ({
        ...prev,
        [toolName]: { ...prev[toolName], result: toolOutput, streaming: false, error: null },
      }));
      setExecutingTools(prev => {
        const newSet = new Set(prev);
        newSet.delete(toolName);
        return newSet;
      });
      setActiveStreams(prev => {
        const newStreams = { ...prev };
        delete newStreams[toolName]; // Remove stream as it's complete
        return newStreams;
      });
    } else if (message && message.data) {
        console.log(`SSE Message for ${toolName} with data, but not expected result structure:`, message.data);
        setToolResults(prev => ({
            ...prev,
            [toolName]: { ...prev[toolName], result: message.data, streaming: false, error: "Received success, but result format was unexpected." }
        }));
        setExecutingTools(prev => { const newSet = new Set(prev); newSet.delete(toolName); return newSet; });
        setActiveStreams(prev => { const newStreams = { ...prev }; delete newStreams[toolName]; return newStreams; });
    }
  }, []);

  const handleSSEStreamError = useCallback((toolName: string, error: Event) => {
    console.error(`SSE Error in MCPDetailPage for ${toolName}:`, error);
    setToolResults(prev => ({
      ...prev,
      [toolName]: { ...prev[toolName], error: 'Streaming failed', streaming: false },
    }));
    setExecutingTools(prev => {
      const newSet = new Set(prev);
      newSet.delete(toolName);
      return newSet;
    });
    setActiveStreams(prev => {
      const newStreams = { ...prev };
      delete newStreams[toolName];
      return newStreams;
    });
  }, []);

  const handleSSEStreamClose = useCallback((toolName: string) => {
    console.log(`SSE Connection considered closed for ${toolName} in MCPDetailPage`);
    // Ensure streaming is marked false if not already by message/error
    setToolResults(prev => {
        if (prev[toolName] && prev[toolName].streaming) {
            return {...prev, [toolName]: { ...prev[toolName], streaming: false }};
        }
        return prev;
    });
    // executingTools and activeStreams should have been cleared by onMessage or onError
    // If they weren't (e.g. stream closed abruptly without final message/error), clear them here.
     setExecutingTools(prev => {
      const newSet = new Set(prev);
      if (newSet.has(toolName)) {
        newSet.delete(toolName);
        return newSet;
      }
      return prev;
    });
    setActiveStreams(prev => {
      if (prev[toolName]) {
        const newStreams = { ...prev };
        delete newStreams[toolName];
        return newStreams;
      }
      return prev;
    });
  }, []);


  useEffect(() => {
    if (initialEnvConfigKeys.length > 0) {
      setEnvConfigKeys(initialEnvConfigKeys);
    }
  }, [initialEnvConfigKeys]);

  useEffect(() => {
    return () => {
      // Component unmount cleanup - useSSEStream's own cleanup handles individual streams
      // No need to iterate and close here as each useSSEStream instance manages its own lifecycle.
      console.log("MCPDetailPage unmounting - individual streams managed by their hooks.");
    };
  }, []);

  const navigateToTool = (toolName: string) => {
    setActiveTab('tools');
    setExpandedTool(toolName);
  };

  const areRequiredParametersFilled = (toolName: string) => {
    const tool = tools.find(t => t.name === toolName);
    if (!tool) return false;
    const requiredParams = tool.input_schema?.required || [];
    const currentToolParams = toolParameters[toolName] || {};
    return requiredParams.every(param => currentToolParams[param] && currentToolParams[param].trim() !== '');
  };

  const handleRunTool = async (toolName: string) => {
    console.log(`Run tool: ${toolName} (Connected: ${isMcpConnected})`);
    if (!isMcpConnected) {
      console.error('MCP not connected');
      return;
    }
    if (executingTools.has(toolName) || activeStreams[toolName]) {
      console.log(`Tool ${toolName} is already executing or has an active stream.`);
      return;
    }

    const tool = tools.find(t => t.name === toolName);
    if (!tool) {
      console.error('Tool not found:', toolName);
      return;
    }
    const currentToolParams = toolParameters[toolName] || {};
    setExecutingTools(prev => new Set(prev).add(toolName));
    // Initialize results for this tool run
    setToolResults(prev => ({
        ...prev,
        [toolName]: {
          timestamp: new Date().toLocaleTimeString(),
          parameters: currentToolParams,
          streaming: true, // Set streaming true immediately
          result: null,
          error: null
        }
      }));

    try {
      const result = await toolExecution.mutateAsync({
        mcp_id: item.id,
        tool_name: toolName,
        tool_parameters: currentToolParams,
      });
      console.log('Tool execution API call result:', result);
      if (result.request_id) {
        setActiveStreams(prev => ({ ...prev, [toolName]: result.request_id }));
        // No need to update toolResults here for streaming, already done
      } else {
        // Handle cases where request_id might not be returned, though expected
        console.error('No request_id received for tool execution:', toolName);
        setToolResults(prev => ({
          ...prev,
          [toolName]: { ...prev[toolName], error: 'Failed to get stream request ID', streaming: false },
        }));
        setExecutingTools(prev => { const newSet = new Set(prev); newSet.delete(toolName); return newSet; });
      }
    } catch (error) {
      console.error('Tool execution API call failed:', error);
      setToolResults(prev => ({
        ...prev,
        [toolName]: { ...prev[toolName], error: 'Tool execution failed', streaming: false },
      }));
      setExecutingTools(prev => { const newSet = new Set(prev); newSet.delete(toolName); return newSet; });
    }
  };

  const fetchEnvDetails = async () => {
    try {
      setIsLoadingEnvDetails(true);
      const response = await api.mcp.getEnvDetails(item.id);
      if (response.data.success) {
        setEnvConfigKeys(response.data.defined_env_keys);
        const existingValues: Record<string, string> = {};
        response.data.env_key_values.forEach(envValue => {
          existingValues[envValue.key] = envValue.value;
        });
        setUserEnvValues(existingValues);
        console.log('Environment details fetched:', response.data);
      }
    } catch (error) {
      console.error('Error fetching environment details:', error);
      setEnvConfigKeys(initialEnvConfigKeys);
    } finally {
      setIsLoadingEnvDetails(false);
    }
  };

  const handleOpenConnectionSheet = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }
    await fetchEnvDetails();
    setIsSheetOpen(true);
  };

  const closeLoginDialog = () => {
    setShowLoginDialog(false);
  };

  const handleSheetConnect = async (submittedEnvValues: Record<string, string>) => {
    if (!user?.id) {
      console.error('User ID not available');
      return;
    }
    setIsConnecting(true);
    console.log("Attempting to connect with (MCPDetailPage):", submittedEnvValues);
    try {
      const envCredentialStatus = item.rawData?.env_credential_status;
      const hasExistingValues = Object.keys(userEnvValues).length > 0;
      const hasModifiedValues = envConfigKeys.some(key => {
        const currentValue = submittedEnvValues[key.key] || '';
        const existingValue = userEnvValues[key.key] || '';
        return currentValue !== existingValue;
      });
      const createContainerRequest: CreateContainerRequest = {
        mcp_id: item.id,
        user_id: user.id,
        type: "stdio" as const,
      };
      if ((!hasExistingValues && envCredentialStatus === "pending_input") || hasModifiedValues) {
        const envArray = envConfigKeys
          .filter(key => submittedEnvValues[key.key])
          .map(key => ({ key: key.key, value: submittedEnvValues[key.key] }));
        if (envArray.length > 0) {
          createContainerRequest.env = envArray;
        }
      }
      console.log('Container creation request:', createContainerRequest);
      const response = await api.mcp.createContainer(createContainerRequest);
      if (response.data.success) {
        setIsMcpConnected(true);
        setIsSheetOpen(false);
        console.log('Container created successfully:', response.data.container_id);
      } else {
        setIsMcpConnected(false);
        console.error('Container creation failed:', response.data.message);
      }
    } catch (error) {
      console.error('Error creating container:', error);
      setIsMcpConnected(false);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <BaseDetailPage item={item} relatedItems={relatedItems} refetch={refetch}>
      {/* Render ToolSSEStream components for tools that have an active stream request ID */}
      {tools.map(tool => (
        activeStreams[tool.name] ? (
          <ToolSSEStream
            key={`${tool.name}-${activeStreams[tool.name]}`} // Key includes requestId for re-mount if requestId changes for the same tool
            toolName={tool.name}
            requestId={activeStreams[tool.name]}
            onMessageReceived={handleSSEMessageReceived}
            onStreamError={handleSSEStreamError}
            onStreamClose={handleSSEStreamClose}
          />
        ) : null
      ))}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full mb-4 justify-start gap-2">
          <TabsTrigger value="overview" className="w-20">Overview</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-muted-foreground mb-4">{item.description}</p>
          </div>
          {tools.length > 0 ? (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Available Tools</h2>
              <div className="grid gap-4 md:grid-cols-2">
                {tools.map((tool) => (
                  <Card
                    key={tool.name}
                    className="group border border-gray-200 dark:border-gray-800 hover:border-primary/50 dark:hover:border-primary/50 transition-all cursor-pointer"
                    onClick={() => navigateToTool(tool.name)}
                  >
                    <CardContent className="pt-6 flex justify-between items-start">
                      <div className="flex-1 pr-4">
                        <h3 className="font-medium mb-2 text-primary group-hover:text-primary/80">{tool.name}</h3>
                        <p className="text-sm text-muted-foreground">{tool.description}</p>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="h-5 w-5 text-primary" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : null}
        </TabsContent>

        <TabsContent value="tools">
          <div className="mb-8">
            {activeTab === 'tools' && !isMcpConnected && (
              <Card className="mb-6 bg-amber-50 border-amber-200 dark:bg-amber-900/30 dark:border-amber-700">
                <CardContent className="pt-6 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-6 w-6 text-amber-500 dark:text-amber-400" />
                    <div>
                      {item.rawData.config[0].type!=="stdio" ? (
                        <>
                        <p className="text-sm text-amber-600 dark:text-amber-400/80">Connection to this MCP server is not supported.</p>
                        <p className="text-sm text-amber-600 dark:text-amber-400/80">Add this MCP to test it.</p>
                        </>

                      ): (
                        <>
                        <h3 className="font-semibold text-amber-700 dark:text-amber-300">Server Connection Required</h3>
                        <p className="text-sm text-amber-600 dark:text-amber-400/80">Connect to your MCP server to test tools.</p>
                      </>
                      )}
                    </div>
                  </div>
                  <Button onClick={handleOpenConnectionSheet} disabled={item.rawData.config[0].type!=="stdio"} variant="default" className="bg-primary hover:bg-primary/90">
                    <Settings className="mr-2 h-4 w-4" /> Connect
                  </Button>
                </CardContent>
              </Card>
            )}
            {activeTab === 'tools' && isMcpConnected && (
              <Card className="mb-6 bg-green-50 border-green-200 dark:bg-green-900/30 dark:border-green-700">
                <CardContent className="pt-6 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Zap className="h-6 w-6 text-green-500 dark:text-green-400" />
                    <div>
                      <h3 className="font-semibold text-green-700 dark:text-green-300">Server Connected</h3>
                      <p className="text-sm text-green-600 dark:text-green-400/80">You can now run the available tools.</p>
                    </div>
                  </div>
                  <Button onClick={handleOpenConnectionSheet} variant="outline">
                    <Settings className="mr-2 h-4 w-4" /> Configure
                  </Button>
                </CardContent>
              </Card>
            )}

            <h2 className="text-xl font-semibold mb-4">Available Tools</h2>
            <p className="text-muted-foreground mb-6">This MCP provides the following tools and capabilities.</p>
            <div className="space-y-4">
              {tools.length > 0 ? (
                tools.map((tool) => {
                  const isExpanded = expandedTool === tool.name;
                  const parametersToDisplay = tool.input_schema?.properties || {};
                  const hasParametersToDisplay = Object.keys(parametersToDisplay).length > 0;
                  const toolResultData = toolResults[tool.name];
                  const isToolExecuting = executingTools.has(tool.name) || (toolResultData?.streaming === true);


                  return (
                    <Card
                      key={tool.name}
                      className={`border ${isExpanded ? 'border-primary/50' : 'border-gray-200 dark:border-gray-800'} transition-all`}
                    >
                      <CardContent className="pt-6">
                        <div
                          className="flex justify-between items-center cursor-pointer"
                          onClick={() => setExpandedTool(isExpanded ? null : tool.name)}
                        >
                          <div>
                            <h3 className="font-medium text-primary">{tool.name}</h3>
                            <p className="text-sm text-muted-foreground mt-1">{tool.description}</p>
                          </div>
                          <div>{isExpanded ? <ChevronUp className="h-5 w-5 text-muted-foreground" /> : <ChevronDown className="h-5 w-5 text-muted-foreground" />}</div>
                        </div>

                        {isExpanded && (
                          <div className="mt-4 pt-4 border-t">
                            {hasParametersToDisplay && (() => {
                              const requiredParams = Object.entries(parametersToDisplay).filter(([paramName]) => tool.input_schema?.required?.includes(paramName));
                              const optionalParams = Object.entries(parametersToDisplay).filter(([paramName]) => !tool.input_schema?.required?.includes(paramName));
                              const isOptionalExpanded = expandedOptionalParams === tool.name;
                              return (
                                <>
                                  {requiredParams.length > 0 && (
                                    <>
                                      <h4 className="text-sm font-medium mb-3">Required Parameters</h4>
                                      <div className="space-y-4 mb-4">
                                        {requiredParams.map(([paramName, param]) => (
                                          <div key={paramName} className="space-y-2">
                                            <div className="flex items-center gap-1">
                                              <span className="text-sm font-medium">{paramName}</span>
                                              <span className="text-xs text-red-500">*</span>
                                            </div>
                                            {param.type === 'string' && param.description?.toLowerCase().includes('long') ? (
                                              <Textarea
                                                value={toolParameters[tool.name]?.[paramName] || ''}
                                                onChange={(e) => setToolParameters(prev => ({ ...prev, [tool.name]: { ...prev[tool.name], [paramName]: e.target.value }}))}
                                                placeholder={param.description} className="text-sm" rows={3}
                                              />
                                            ) : (
                                              <Input
                                                value={toolParameters[tool.name]?.[paramName] || ''}
                                                onChange={(e) => setToolParameters(prev => ({ ...prev, [tool.name]: { ...prev[tool.name], [paramName]: e.target.value }}))}
                                                placeholder={param.description} className="text-sm"
                                              />
                                            )}
                                            <div className="flex gap-2 text-xs text-muted-foreground"><span>Type: {param.type}</span></div>
                                          </div>
                                        ))}
                                      </div>
                                    </>
                                  )}
                                  {optionalParams.length > 0 && (
                                    <>
                                      <div
                                        className="flex items-center justify-between cursor-pointer py-2 hover:bg-muted/50 rounded-md px-2 -mx-2"
                                        onClick={() => setExpandedOptionalParams(isOptionalExpanded ? null : tool.name)}
                                      >
                                        <h4 className="text-sm font-medium">Optional Parameters ({optionalParams.length})</h4>
                                        {isOptionalExpanded ? <ChevronUp className="h-4 w-4 text-muted-foreground" /> : <ChevronDown className="h-4 w-4 text-muted-foreground" />}
                                      </div>
                                      {isOptionalExpanded && (
                                        <div className="space-y-4 mb-4 mt-2">
                                          {optionalParams.map(([paramName, param]) => (
                                            <div key={paramName} className="space-y-2">
                                              <div className="flex items-center gap-1"><span className="text-sm font-medium">{paramName}</span></div>
                                              {param.type === 'string' && param.description?.toLowerCase().includes('long') ? (
                                                <Textarea
                                                  value={toolParameters[tool.name]?.[paramName] || ''}
                                                  onChange={(e) => setToolParameters(prev => ({ ...prev, [tool.name]: { ...prev[tool.name], [paramName]: e.target.value }}))}
                                                  placeholder={param.description} className="text-sm" rows={3}
                                                />
                                              ) : (
                                                <Input
                                                  value={toolParameters[tool.name]?.[paramName] || ''}
                                                  onChange={(e) => setToolParameters(prev => ({ ...prev, [tool.name]: { ...prev[tool.name], [paramName]: e.target.value }}))}
                                                  placeholder={param.description} className="text-sm"
                                                />
                                              )}
                                              <div className="flex gap-2 text-xs text-muted-foreground"><span>Type: {param.type}</span></div>
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                    </>
                                  )}
                                </>
                              );
                            })()}
                            <Button
                              onClick={() => handleRunTool(tool.name)}
                              disabled={!isMcpConnected || isToolExecuting || !areRequiredParametersFilled(tool.name)}
                              className="w-full"
                            >
                              {isToolExecuting ? ( <><Clock className="mr-2 h-4 w-4 animate-spin" />Running...</>
                              ) : !isMcpConnected ? ( <><Play className="mr-2 h-4 w-4" />Connect Server First</>
                              ) : !areRequiredParametersFilled(tool.name) ? ( <><Play className="mr-2 h-4 w-4" />Fill Required Parameters</>
                              ) : ( <><Play className="mr-2 h-4 w-4" />Run Tool</>
                              )}
                            </Button>

                            {toolResultData && (
                              <div className="mt-6 pt-4 border-t">
                                <div className="flex items-center justify-between mb-3">
                                  <div className="flex items-center gap-2">
                                    {toolResultData.streaming ? <Clock className="h-4 w-4 text-blue-500 animate-spin" />
                                      : toolResultData.error ? <div className="h-4 w-4 rounded-full bg-red-500" />
                                      : <CheckCircle className="h-4 w-4 text-green-500" />
                                    }
                                    <h5 className="text-sm font-medium">{toolResultData.streaming ? 'Processing...' : 'Result'}</h5>
                                  </div>
                                  <span className="text-xs text-muted-foreground">{toolResultData.timestamp}</span>
                                </div>
                                {toolResultData.parameters && Object.keys(toolResultData.parameters).length > 0 && (
                                  <div className="mb-4">
                                    {Object.entries(toolResultData.parameters).map(([key, value]) => (
                                      <div key={key} className="mb-2">
                                        <div className="flex items-center gap-1 mb-1">
                                          <span className="text-xs font-medium">{key}</span>
                                          <span className="text-xs text-red-500">*</span>
                                          <span className="text-xs text-muted-foreground">string</span>
                                        </div>
                                        <div className="bg-muted p-2 rounded text-xs font-mono">{String(value)}</div>
                                        <div className="text-xs text-muted-foreground mt-1">Input data for LLM Integration processing</div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                                {!toolResultData.streaming && (toolResultData.result || toolResultData.error) && (
                                  <div className="bg-muted p-3 rounded-md">
                                    <pre className="text-xs whitespace-pre-wrap overflow-x-auto">
                                      {toolResultData.error ? toolResultData.error : JSON.stringify(toolResultData.result, null, 2)}
                                    </pre>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })
              ) : (
                <Card><CardContent className="pt-6"><p className="text-muted-foreground">No tools available for this MCP.</p></CardContent></Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="integration">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">API Integration</h2>
            <p className="text-muted-foreground mb-4">Integrate this MCP into your applications.</p>
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Get your API Key</h3>
              <p className="text-muted-foreground mb-3">You'll need to sign in to the RUH Developer Portal and generate an API key to connect to this server.</p>
              <Button variant="secondary" className="bg-gray-800 hover:bg-gray-700 text-white dark:bg-primary/90 dark:hover:bg-primary dark:text-white dark:border-primary/30" onClick={redirectToDeveloperPlatform}>Generate API Key</Button>
            </div>
            <div className="mt-8 mb-8 border-t pt-4">
              <h3 className="text-lg font-medium mb-4">Installation Details</h3>
              <div className="space-y-4">
                <div className="flex gap-2 justify-start">
                  <Button variant={installMethod === 'json' ? 'default' : 'outline'} size="sm" onClick={() => setInstallMethod('json')} className="w-24">JSON</Button>
                  <Button variant={installMethod === 'url' ? 'default' : 'outline'} size="sm" onClick={() => setInstallMethod('url')} className="w-24">URL</Button>
                </div>
                <div className="bg-muted p-3 rounded-md text-xs font-mono overflow-x-auto">
                  {installMethod === 'json' ? (
                    <pre>{`{\n  "id": "${item.id}",\n  "name": "${item.title}",\n  "type": "${item.type.toLowerCase()}"\n}`}</pre>
                  ) : (
                    <pre>{`https://api.ruh.ai/marketplace/${item.type.toLowerCase()}/${item.id}`}</pre>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-8">
              <h3 className="text-lg font-medium mb-3">Code Example</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
                  <code>{`// JavaScript Example
const fetchData = async () => {
  try {
    const response = await axios.get('https://api.ruh.ai/mcp/${item.id}', {
      headers: {
        'Authorization': 'Bearer your-ruh-api-key',
        'Content-Type': 'application/json'
      }
    });
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};`}</code>
                </pre>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      <McpConnectionSheet
        isOpen={isSheetOpen}
        onOpenChange={setIsSheetOpen}
        envConfigKeys={envConfigKeys}
        onConnect={handleSheetConnect}
        isConnected={isMcpConnected}
        isLoading={isConnecting}
        mcpTitle={item.title}
        userEnvValues={userEnvValues}
      />
      {/* Login Dialog */}
      <LoginDialog
        isOpen={showLoginDialog}
        onOpenChange={closeLoginDialog}
      />
    </BaseDetailPage>
);
}
