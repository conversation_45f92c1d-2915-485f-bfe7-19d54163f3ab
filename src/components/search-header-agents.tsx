'use client';

import { AgentMiniCard } from './agent-mini-card';

export function SearchHeaderAgents() {
  return (
    <>
      {/* Left side cards - 1 Agent, 1 MCP, 1 Workflow */}
      <AgentMiniCard
        name="Data Analyst"
        avatar="/assets/agent.png"
        skills={['Data Processing', 'Visualization']}
        type="AGENT"
        position="left"
        className="top-24 left-6 md:left-20 rotate-[-5deg] z-10 [animation-delay:0.1s]"
      />

      <AgentMiniCard
        name="Research Protocol"
        avatar="/assets/mcp.png"
        skills={['Multi-Agent', 'Collaboration']}
        type="MCP"
        position="left"
        className="top-52 left-12 md:left-32 rotate-[3deg] z-10 [animation-delay:0.5s]"
      />

      <AgentMiniCard
        name="Data Pipeline"
        avatar="/assets/workflow.png"
        skills={['ETL', 'Automation']}
        type="WORKFLOW"
        position="left"
        className="bottom-24 left-8 md:left-24 rotate-[-8deg] z-10 [animation-delay:0.9s]"
      />

      {/* Right side cards - 1 Agent, 1 MCP, 1 Workflow */}
      <AgentMiniCard
        name="NLP Specialist"
        avatar="/assets/agent.png"
        skills={['Text Analysis', 'Sentiment']}
        type="AGENT"
        position="right"
        className="top-20 right-6 md:right-20 rotate-[6deg] z-10 [animation-delay:0.3s]"
      />

      <AgentMiniCard
        name="Cognitive Network"
        avatar="/assets/mcp.png"
        skills={['Distributed', 'Reasoning']}
        type="MCP"
        position="right"
        className="top-48 right-12 md:right-32 rotate-[-4deg] z-10 [animation-delay:0.7s]"
      />

      <AgentMiniCard
        name="Content Pipeline"
        avatar="/assets/workflow.png"
        skills={['Generation', 'Publishing']}
        type="WORKFLOW"
        position="right"
        className="bottom-28 right-8 md:right-24 rotate-[7deg] z-10 [animation-delay:1.1s]"
      />
    </>
  );
}
