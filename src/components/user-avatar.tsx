'use client';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Define a more flexible user type to handle different user data structures
type FlexibleUser = {
  name?: string;
  fullName?: string;
  email?: string;
  profileImage?: string;
  avatarUrl?: string;
  [key: string]: unknown;
};

interface UserAvatarProps {
  user: FlexibleUser | null;
  className?: string;
  fallback?: string;
}

export function UserAvatar({ user, className = "", fallback = "U" }: UserAvatarProps) {
  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!user) {
      return fallback;
    }

    // Prioritize fullName over name as per the new API response
    const nameValue = user.fullName || user.name || '';

    if (!nameValue) {
      return fallback;
    }

    // Handle case where name might be empty or not a string
    const name = typeof nameValue === 'string' ? nameValue.trim() : '';
    if (!name) {
      return fallback;
    }

    // Split by spaces and get first letter of each part
    const nameParts = name.split(" ").filter(part => part.length > 0);

    if (nameParts.length === 0) {
      return fallback;
    }

    // If only one name part, take up to two characters from it
    if (nameParts.length === 1) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }

    // Otherwise take first letter of first and last parts
    return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
  };

  // Get profile image URL (prioritize profileImage over avatarUrl)
  const getProfileImageUrl = () => {
    return user?.profileImage || user?.avatarUrl || '';
  };

  const profileImageUrl = getProfileImageUrl();

  return (
    <Avatar className={className}>
      {profileImageUrl && (
        <AvatarImage
          src={profileImageUrl}
          alt={user?.fullName || user?.name || 'User'}
          className="object-cover"
        />
      )}
      <AvatarFallback
        className="bg-blue-600 text-white font-medium flex items-center justify-center"
        style={{
          fontSize: className.includes('h-8') ? '0.875rem' : '1rem',
          width: '100%',
          height: '100%'
        }}
      >
        {getUserInitials()}
      </AvatarFallback>
    </Avatar>
  );
}
