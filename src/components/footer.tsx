import Link from "next/link";
import Image from "next/image";

export function Footer() {
  return (
    <footer className="border-t border-gray-200 bg-[#14142b] dark:border-gray-800 dark:bg-[#14142b]">
      <div className="container pt-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Image
                src="/logo.svg"
                alt="RUH Logo"
                width={32}
                height={32}
                className="h-10 w-20"
              />
              {/* <div className="flex flex-col">
                <span className="text-lg font-bold leading-none text-primary">RUH</span>
                <span className="text-xs text-white dark:text-white">marketplace</span>
              </div> */}
            </div>
            <p className="text-sm text-white/80 dark:text-white/80">
              The place to discover the latest AI agents, protocols, and workflows to enhance your AI capabilities.
            </p>
            <p className="text-left text-xs text-white/80 dark:text-white/80 mt-4">
              &copy; {new Date().getFullYear()} RUH Marketplace. All rights reserved.
            </p>
          </div>
          <div>
            <h3 className="text-sm font-semibold mb-4 text-white dark:text-white">Marketplace</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/mcps"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  MCPs
                </Link>
              </li>
              <li>
                <Link
                  href="/agents"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Agents
                </Link>
              </li>
              <li>
                <Link
                  href="/workflows"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Workflows
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-sm font-semibold mb-4 text-white dark:text-white">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/about"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  About
                </Link>
              {/* </li>
              <li> */}
                {/* <Link
                  href="/blog"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Blog
                </Link> */}
                {/* </li>
              <li> */}
                {/* <Link
                  href="/careers"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Careers
                </Link> */}
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-sm font-semibold mb-4 text-white dark:text-white">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/privacy"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-sm text-white/70 hover:text-white transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12">
        </div>
        <div className="relative h-[80px] sm:h-[120px] md:h-[150px] overflow-hidden">
          <div
            className="absolute bottom-[-60px] sm:bottom-[-90px] md:bottom-[-130px] lg:bottom-[-160px] xl:bottom-[-180px] left-0 right-0 text-center text-[80px] sm:text-[120px] md:text-[180px] lg:text-[220px] xl:text-[250px] font-extrabold text-transparent pointer-events-none select-none tracking-[3px] sm:tracking-[5px] md:tracking-[8px] lg:tracking-[10px]"
            style={{
              WebkitTextStroke: '2px rgba(255, 255, 255, 0.25)'
            }}
          >
            RUH AI
          </div>
        </div>
      </div>
    </footer>
  );
}
