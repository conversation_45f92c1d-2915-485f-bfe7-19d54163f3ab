'use client';

import { notFound, useParams } from "next/navigation";
import { DynamicMarketplacePage } from "@/components/dynamic-marketplace-page";

// Define the valid types and their corresponding tabs
const typeConfig = {
  agents: {
    defaultTab: "agents"
  },
  mcps: {
    defaultTab: "mcps"
  },
  workflows: {
    defaultTab: "workflows"
  }
};

interface MarketplaceTypePageClientProps {
  type?: string;
}

export function MarketplaceTypePageClient({ type: propType }: MarketplaceTypePageClientProps) {
  const params = useParams();
  const type = propType || (params.type as string);

  // Check if the type is valid
  if (!type || !Object.keys(typeConfig).includes(type)) {
    notFound();
  }

  const config = typeConfig[type as keyof typeof typeConfig];

  return (
    <DynamicMarketplacePage
      defaultTab={config.defaultTab}
    />
  );
}
