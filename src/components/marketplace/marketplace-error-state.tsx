"use client";

import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface MarketplaceErrorStateProps {
  type: "MCP" | "AGENT" | "WORKFLOW";
  onRetry?: () => void;
}

export function MarketplaceErrorState({
  type,
  onRetry,
}: MarketplaceErrorStateProps) {
  const titles = {
    MCP: "Failed to load MCPs",
    AGENT: "Failed to load Agents",
    WORKFLOW: "Failed to load Workflows",
  };

  const messages = {
    MCP: "We couldn't load the Multi-Agent Cognitive Protocols at this time.",
    AGENT: "We couldn't load the Specialized Agents at this time.",
    WORKFLOW: "We couldn't load the Automated Workflows at this time.",
  };

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center bg-card border border-border rounded-lg shadow-sm my-4">
      <div className="rounded-full bg-destructive/10 p-3 mb-4">
        <AlertCircle className="h-6 w-6 text-destructive" />
      </div>
      <h3 className="text-lg font-semibold mb-2">{titles[type]}</h3>
      <p className="text-muted-foreground mb-4 max-w-md">{messages[type]}</p>
      {onRetry && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  );
}
