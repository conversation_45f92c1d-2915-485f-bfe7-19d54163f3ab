'use client';

import { useMarketplaceItems } from '@/hooks';
import { useUIStore } from '@/store';
import { ItemType } from '@/lib/api/types';
import { useState } from 'react';
import Image from 'next/image';
import {
  ItemSkeletonGrid,
  AgentItemSkeletonGrid,
  WorkflowItemSkeletonGrid
} from './item-skeleton';

export function MarketplaceItemsList() {
  const { activeTab, setActiveTab, searchQuery, setSearchQuery, filters, setFilters } = useUIStore();
  const [page, setPage] = useState(1);
  const limit = 10;

  const { data, isLoading, error } = useMarketplaceItems({
    page,
    limit,
  });

  const handleTabChange = (tab: ItemType) => {
    setActiveTab(tab);
    setPage(1);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPage(1);
  };

  const handleSortChange = (sortBy: 'rating' | 'downloads' | 'price' | 'createdAt') => {
    setFilters({ sortBy });
    setPage(1);
  };

  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex space-x-4">
            <button
              className={`px-4 py-2 rounded-md ${activeTab === 'MCP' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
            >
              MCPs
            </button>
            <button
              className={`px-4 py-2 rounded-md ${activeTab === 'AGENT' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
            >
              Agents
            </button>
            <button
              className={`px-4 py-2 rounded-md ${activeTab === 'WORKFLOW' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
            >
              Workflows
            </button>
          </div>
        </div>

        {activeTab === 'MCP' && <ItemSkeletonGrid count={6} />}
        {activeTab === 'AGENT' && <AgentItemSkeletonGrid count={6} />}
        {activeTab === 'WORKFLOW' && <WorkflowItemSkeletonGrid count={6} />}
      </div>
    );
  }

  if (error) {
    return <div>Error loading marketplace items</div>;
  }

  if (!data || data.data.length === 0) {
    return <div>No items found</div>;
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex space-x-4">
          <button
            onClick={() => handleTabChange('MCP')}
            className={`px-4 py-2 rounded-md ${activeTab === 'MCP' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
          >
            MCPs
          </button>
          <button
            onClick={() => handleTabChange('AGENT')}
            className={`px-4 py-2 rounded-md ${activeTab === 'AGENT' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
          >
            Agents
          </button>
          <button
            onClick={() => handleTabChange('WORKFLOW')}
            className={`px-4 py-2 rounded-md ${activeTab === 'WORKFLOW' ? 'bg-primary text-white' : 'bg-gray-100 dark:bg-gray-800'}`}
          >
            Workflows
          </button>
        </div>

        <div className="flex items-center space-x-4">
          <input
            type="search"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="px-4 py-2 border rounded-md"
          />

          <select
            value={filters.sortBy}
            onChange={(e) => handleSortChange(e.target.value as 'rating' | 'downloads' | 'price' | 'createdAt')}
            className="px-4 py-2 border rounded-md"
          >
            <option value="rating">Rating</option>
            <option value="downloads">Downloads</option>
            <option value="price">Price</option>
            <option value="createdAt">Newest</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.data.map((item) => (
          <div key={item.id} className="border rounded-lg overflow-hidden shadow-sm">
            <div className="aspect-square relative">
              <Image
                src={item.imageSrc}
                alt={item.title}
                fill
                className="object-cover w-full h-full"
              />
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
                  {item.type}
                </span>
                <span className="text-xs font-medium px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
                  {item.category}
                </span>
              </div>
              <h3 className="text-lg font-semibold mt-2">{item.title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                {item.description}
              </p>
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center">
                  <span className="text-sm font-medium">⭐ {item.rating}</span>
                  <span className="mx-2">•</span>
                  <span className="text-sm">{item.downloads} downloads</span>
                </div>
                <span className="text-sm font-medium">{item.author}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {data.totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className="px-4 py-2 rounded-md bg-gray-100 dark:bg-gray-800 disabled:opacity-50"
            >
              Previous
            </button>
            <div className="flex items-center px-4">
              Page {page} of {data.totalPages}
            </div>
            <button
              onClick={() => setPage(Math.min(data.totalPages, page + 1))}
              disabled={page === data.totalPages}
              className="px-4 py-2 rounded-md bg-gray-100 dark:bg-gray-800 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
