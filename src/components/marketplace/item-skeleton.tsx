import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";

export function MarketplaceItemSkeleton() {
  return (
    <Card className="h-full overflow-hidden transition-all flex flex-col rounded-xl">
      <div className="flex items-start p-4 gap-4">
        {/* Avatar skeleton */}
        <div className="h-16 w-16 relative overflow-hidden rounded-full">
          <Skeleton className="h-full w-full absolute inset-0" />
        </div>

        <div className="flex-1 min-w-0">
          {/* Title and rating */}
          <div className="flex justify-between items-start mb-2">
            <div>
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-4 w-36 mt-1" />
            </div>

            {/* Rating */}
            <div className="flex items-center ml-3 shrink-0">
              <Skeleton className="h-5 w-12" />
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-1 mt-2">
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-14 rounded-full" />
          </div>

          {/* Type tag */}
          <div className="mt-2">
            <Skeleton className="h-6 w-14 rounded-full" />
          </div>
        </div>
      </div>
    </Card>
  );
}

export function ItemSkeletonGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array(count)
        .fill(null)
        .map((_, index) => (
          <MarketplaceItemSkeleton key={index} />
        ))}
    </div>
  );
}

export function AgentItemSkeleton() {
  // Use the same skeleton for all item types
  return <MarketplaceItemSkeleton />;
}

export function AgentItemSkeletonGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array(count)
        .fill(null)
        .map((_, index) => (
          <AgentItemSkeleton key={index} />
        ))}
    </div>
  );
}

export function WorkflowItemSkeleton() {
  // Use the same skeleton for all item types
  return <MarketplaceItemSkeleton />;
}

export function WorkflowItemSkeletonGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {Array(count)
        .fill(null)
        .map((_, index) => (
          <WorkflowItemSkeleton key={index} />
        ))}
    </div>
  );
}
