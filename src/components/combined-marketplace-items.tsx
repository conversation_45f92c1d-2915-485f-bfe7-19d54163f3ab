// 'use client';

// import { MarketplaceItemCard } from '@/components/marketplace-item-card';
// import { ItemSkeletonGrid } from '@/components/marketplace/item-skeleton';
// import { SectionHeader } from '@/components/section-header';
// import { ErrorState } from '@/components/ui/error-state';
// import { useCombinedMarketplace } from '@/hooks/use-combined-marketplace';
// import { Agent, MCP, Workflow } from '@/lib/api/types/marketplace-items';

// interface CombinedMarketplaceItemsProps {
//   limit?: number;
//   showHeader?: boolean;
//   title?: string;
// }

// /**
//  * Component for displaying combined marketplace items (MCPs, Agents, and Workflows)
//  * Used for the "All" tab on the home page
//  */
// export function CombinedMarketplaceItems({
//   limit = 100, // Increased limit to show more items
//   showHeader = true,
//   title = "All Marketplace Items"
// }: CombinedMarketplaceItemsProps) {
//   // Fetch combined marketplace items
//   const { data, isLoading, error } = useCombinedMarketplace({
//     limit,
//   });

//   // Determine the type of an item
//   const getItemType = (item: MCP | Agent | Workflow) => {
//     // If the item has an item_type property from the API, use it
//     if (item.item_type) {
//       return item.item_type;
//     }

//     // Otherwise, determine the type based on properties
//     if ('agent_category' in item) {
//       return 'AGENT';
//     } else if ('execution_count' in item) {
//       return 'WORKFLOW';
//     } else {
//       return 'MCP';
//     }
//   };

//   // Get the appropriate image source for an item
//   const getImageSrc = (item: MCP | Agent | Workflow) => {
//     const type = getItemType(item);

//     switch (type) {
//       case 'AGENT':
//         return '/assets/agent.png';
//       case 'WORKFLOW':
//         return '/assets/workflow.png';
//       case 'MCP':
//       default:
//         return '/assets/mcp.png';
//     }
//   };

//   // Get the category for an item
//   const getCategory = (item: MCP | Agent | Workflow) => {
//     if ('agent_category' in item) {
//       return item.agent_category || 'General';
//     } else if (item.category) {
//       return item.category;
//     } else {
//       return 'General';
//     }
//   };

//   // Render loading state
//   if (isLoading) {
//     return <ItemSkeletonGrid count={10} />;
//   }

//   // Render error state
//   if (error) {
//     return (
//       <ErrorState
//         title="Failed to load marketplace items"
//         message="We couldn't load the marketplace items at this time. Please try again later."
//         onRetry={() => window.location.reload()}
//       />
//     );
//   }

//   // Render empty state
//   if (!data) {
//     return (
//       <div className="text-center py-8">
//         <p>No marketplace items available at the moment.</p>
//       </div>
//     );
//   }

//   // Render marketplace items

//   console.log("data templates:", data);
//   return (
//     <div className="space-y-6">
//       {showHeader && (
//         <SectionHeader
//           title={title}
//           description="Discover and deploy a variety of AI solutions"
//         />
//       )}

//       <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
//         {data.templates.map((item) => {
//           const itemType = getItemType(item);
//           console.log('Item:', item.id, 'item_type:', item.item_type, 'determined type:', itemType);

//           return (
//             <div key={item.id}>
//               <MarketplaceItemCard
//                 id={item.id}
//                 title={item.name}
//                 description={item.description}
//                 category={getCategory(item)}
//                 owner_name={item.owner_name || 'RUH AI'}
//                 imageSrc={getImageSrc(item)}
//                 type={itemType as 'MCP' | 'AGENT' | 'WORKFLOW'}
//                 rating={item.rating || item.average_rating || 0}
//                 use_count={item.use_count || item.downloads || 0}
//                 tags={item.tags}
//               />
//             </div>
//           );
//         })}
//       </div>
//     </div>
//   );
// }
