import { ItemAvatar } from "@/components/item-avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardDescription, CardTitle } from "@/components/ui/card";
import { Star } from "lucide-react";
import Link from "next/link";

interface MarketplaceItemProps {
  id: string;
  title: string;
  description: string;
  type: "MCP" | "AGENT" | "WORKFLOW";
  rating?: number;
  use_count?: number; // Number of times the item has been used/downloaded

  // Optional fields
  category?: string;
  owner_name?: string; // Previously author
  avatar?: string; // URL to avatar image
  imageSrc?: string; // Image source URL

  // Additional fields from API responses
  department?: string;
  tone?: string;
  model_provider?: string;
  model_name?: string;
  workflow_ids?: string[];
  mcp_server_ids?: string[];
  created_at?: string;
  updated_at?: string;
  tags?: string[] | null;
}

// Helper function to get default image based on item type
const getDefaultImageForType = (type: "MCP" | "AGENT" | "WORKFLOW"): string => {
  switch (type) {
    case "AGENT":
      return "/assets/agent.png";
    case "MCP":
      return "/assets/mcp.png";
    case "WORKFLOW":
      return "/assets/workflow.png";
    default:
      return "/assets/default_avatar.png";
  }
};

export function MarketplaceItemCard({
  id,
  title,
  description,
  type,
  rating = 4.9,
  avatar,
  owner_name = "RUH AI",
  imageSrc,
}: MarketplaceItemProps) {

  // Process tags for display - only show integrations
  const displayTags: string[] = [];

  // Extract integrations from tags
  // if (tags) {
  //   // Case 1: If tags has an 'integrations' array property
  //   if (tags.integrations && Array.isArray(tags.integrations)) {
  //     tags.integrations.forEach(integration => {
  //       displayTags.push(String(integration));
  //     });
  //   }
  //   // Case 2: If tags itself is an object with keys and values
  //   else {
  //     // Look for any array properties that might contain integration tags
  //     Object.entries(tags).forEach(([, value]) => {
  //       if (Array.isArray(value)) {
  //         // If we find an array property, add its items as tags
  //         value.forEach(item => {
  //           displayTags.push(String(item));
  //         });
  //       }
  //     });
  //   }
  // }

  // Ensure type is a string and handle undefined case
  const safeType = type || "UNKNOWN";
  
  // Generate the correct route based on type
  const getDetailRoute = () => {
    switch (safeType.toLowerCase()) {
      case 'agent':
        return `/agents/${id}`;
      case 'mcp':
        return `/mcps/${id}`;
      case 'workflow':
        return `/workflows/${id}`;
      default:
        return `/agents/${id}`; // fallback to agents
    }
  };

  return (
    <Link href={getDetailRoute()}>
      <Card className="h-full min-h-[180px] max-h-[220px] overflow-hidden transition-all hover:shadow-md hover:border-primary/20 dark:hover:border-primary/20 flex flex-col rounded-xl relative">
        {/* Type Badge - Absolute positioned on the left */}
        <div className="absolute top-3 left-3 z-10">
          <Badge
            variant="outline"
            className={`
              ${safeType === 'AGENT' ? 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800' : ''}
              ${safeType === 'WORKFLOW' ? 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900 dark:text-green-300 dark:border-green-800' : ''}
              ${safeType === 'MCP' ? 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700' : ''}
              font-medium text-xs px-2 py-0.5
            `}
          >
            {safeType}
          </Badge>
        </div>

        {/* Rating - Absolute positioned on the right */}
        <div className="absolute top-3 right-3 z-10">
          <div className="flex items-center shrink-0 bg-amber-50 dark:bg-amber-950/30 px-2 py-0.5 rounded-md">
            <Star className="h-4 w-4 text-amber-500 mr-1" />
            <span className="text-xs font-medium text-amber-700 dark:text-amber-300">
              {rating.toFixed(1)}
            </span>
          </div>
        </div>

        <div className="flex items-start p-4 pt-8 gap-4">
          {/* Avatar */}
          <ItemAvatar
            title={title}
            className="h-16 w-16"
            type={safeType}
            imageUrl={avatar || imageSrc || getDefaultImageForType(safeType as "MCP" | "AGENT" | "WORKFLOW")}
          />

          <div className="flex-1 min-w-0">
            {/* Header: Title and Subtitle */}
            <div className="mb-1">
              {/* Title */}
              <div className="flex items-start">
                <CardTitle className="text-base font-semibold line-clamp-1">{title}</CardTitle>
              </div>

              {/* Owner/Author line */}
              <div className="text-xs text-muted-foreground mt-0.5 mb-1.5">
                {owner_name || "RUH AI"}
              </div>
            </div>

            {/* Description */}
            <CardDescription className="text-sm line-clamp-2 mb-2">
              {description}
            </CardDescription>

            {/* Tags - Only showing integrations */}
            {displayTags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {displayTags.slice(0, 3).map((tag, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs h-6 px-2.5 rounded-full bg-primary-50 text-primary-700 dark:bg-primary-950/30 dark:text-primary-300 border-primary-200 dark:border-primary-800"
                  >
                    {tag}
                  </Badge>
                ))}
                {displayTags.length > 3 && (
                  <Badge variant="outline" className="text-xs h-6 px-2.5 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700">
                    +{displayTags.length - 3} more
                  </Badge>
                )}
              </div>
            )}

            {/* Type tag - moved outside the flex container to position it absolutely */}
          </div>
        </div>
      </Card>
    </Link>
  );
}
