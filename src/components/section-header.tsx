import Link from "next/link";

interface SectionHeaderProps {
  title: string;
  description?: string;
  viewAllLink?: string;
  className?: string;
}

export function SectionHeader({
  title,
  description,
  viewAllLink,
}: SectionHeaderProps) {
  return (
    <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center mb-6">
      <div>
        <h2 className="text-2xl font-semibold">
          {title}
        </h2>
        {description && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {description}
          </p>
        )}
      </div>
      {viewAllLink && (
        <Link
          href={viewAllLink}
          className="text-sm font-medium text-primary hover:underline flex items-center gap-1"
        >
          View all <span aria-hidden="true">→</span>
        </Link>
      )}
    </div>
  );
}
