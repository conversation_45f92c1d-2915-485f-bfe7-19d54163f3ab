"use client";

import { BaseDetailPage } from "@/components/base-detail-page";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MarketplaceItem } from "@/data/marketplace-items";
import { redirectToDeveloperPlatform } from "@/lib/helpers";
import { ExternalLink, Server, Workflow as WorkflowIcon, Loader2, Tag, Calendar, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { agentService } from "@/lib/api/services/agent.service";
import { AgentVersionInDB } from "@/lib/api/types/marketplace-items";

interface AgentDetailPageProps {
  item: MarketplaceItem;
  relatedItems?: MarketplaceItem[];
  refetch?: () => void;
}

export function AgentDetailPage({ item, relatedItems = [], refetch }: AgentDetailPageProps) {
  const router = useRouter();

  // State for versions tab
  const [versions, setVersions] = useState<AgentVersionInDB[]>([]);
  const [versionsLoading, setVersionsLoading] = useState(false);
  const [versionsError, setVersionsError] = useState<string | null>(null);
  const [versionsPage, setVersionsPage] = useState(1);
  const [versionsTotalPages, setVersionsTotalPages] = useState(1);

  // Function to navigate to workflow detail page
  const navigateToWorkflowDetail = (templateId: string) => {
    router.push(`/workflows/${templateId}`);
  };

  // Function to navigate to MCP detail page
  const navigateToMCPDetail = (templateId: string) => {
    router.push(`/mcps/${templateId}`);
  };

  // Function to fetch agent versions
  const fetchVersions = async (pageNum: number) => {
    if (!item.rawData.source_agent_id) return;

    setVersionsLoading(true);
    setVersionsError(null);
    try {
      const response = await agentService.getAgentVersions(
        item.rawData.source_agent_id,
        {
          page: pageNum,
          page_size: 10,
        }
      );
      setVersions(response.data.versions);
      setVersionsTotalPages(response.data.total_pages);
    } catch (err) {
      setVersionsError("Failed to load agent versions");
      console.error("Error fetching agent versions:", err);
    } finally {
      setVersionsLoading(false);
    }
  };

  // Load versions when component mounts
  useEffect(() => {
    fetchVersions(1);
  }, [item.id]);

  return (
    <BaseDetailPage item={item} relatedItems={relatedItems} refetch={refetch}>
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full mb-4 justify-start gap-2">
          <TabsTrigger value="overview" className="w-20">Overview</TabsTrigger>
          <TabsTrigger value="tools">Tools & Servers</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="versions">Versions</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-muted-foreground mb-4">
              {item.description}
            </p>
          </div>


          {/* Agent Information */}
          {item.rawData ? (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Agent Information</h2>
              <div className="grid gap-4 md:grid-cols-2">
                {item.rawData.version ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <WorkflowIcon className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">Version</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.rawData.version}
                      </p>
                    </CardContent>
                  </Card>
                ) : null}

                {item.rawData.status ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <WorkflowIcon className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">Status</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {item.rawData.status}
                      </p>
                    </CardContent>
                  </Card>
                ) : null}
              </div>
            </div>
          ) : null}

          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Capabiliities</h2>
            <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
            {
              item.rawData?.agent_capabilities && item.rawData.agent_capabilities.capabilities && item.rawData.agent_capabilities.capabilities.length > 0 ? (
                item.rawData.agent_capabilities.capabilities.map((capability: any) => (
                  <li key={capability.title} className="mb-4">
                    <div className="font-semibold text-foreground">{capability.title}</div>
                    <div className="text-sm text-muted-foreground pl-2">{capability.description}</div>
                  </li>
                ))
              ) : (
                <li>No capabilities found</li>
              )
            }
            </ul>
          </div>
        </TabsContent>

        {/* Tools & Servers Tab Content */}
        <TabsContent value="tools">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">MCP Servers</h2>
            <p className="text-muted-foreground mb-6">
              This agent uses the following MCP servers for enhanced capabilities.
            </p>

            <div className="grid gap-4 md:grid-cols-2">
              {/* Dynamic MCPs from API data */}
              {item.rawData && item.rawData.mcps && Array.isArray(item.rawData.mcps) && item.rawData.mcps.length > 0 ? (
                item.rawData.mcps.map((mcp: any) => (
                  <Card key={mcp.id} className="relative">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Server className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">{mcp.name}</h3>
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        {mcp.description}
                      </p>
                      {mcp.visibility === "public" && mcp.is_imported === false && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => navigateToMCPDetail(mcp.id)}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View in Marketplace
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : item.rawData && item.rawData.mcp_server_ids && Array.isArray(item.rawData.mcp_server_ids) && item.rawData.mcp_server_ids.length > 0 ? (
                // Fallback to mcp_server_ids if mcps array is not available
                item.rawData.mcp_server_ids.map((mcpId: string) => (
                  <Card key={mcpId}>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <Server className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">MCP Server</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {mcpId}
                      </p>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Server className="h-5 w-5 text-muted-foreground" />
                      <h3 className="font-medium">No MCP Servers</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      This agent does not use any MCP servers.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Workflows Tab Content */}
        <TabsContent value="workflows">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Associated Workflows</h2>
            <p className="text-muted-foreground mb-6">
              This agent is used in the following workflows.
            </p>

            <div className="grid gap-4 md:grid-cols-2">
              {/* Dynamic workflows from API data */}
              {item.rawData && item.rawData.workflows && Array.isArray(item.rawData.workflows) && item.rawData.workflows.length > 0 ? (
                item.rawData.workflows.map((workflow: any) => (
                  <Card key={workflow.id} className="relative">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <WorkflowIcon className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">{workflow.name}</h3>
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        {workflow.description}
                      </p>
                      {workflow.visibility === "public" && workflow.is_imported === false && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => navigateToWorkflowDetail(workflow.workflow_template_id || workflow.id)}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View in Marketplace
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : item.rawData && item.rawData.workflow_ids && Array.isArray(item.rawData.workflow_ids) && item.rawData.workflow_ids.length > 0 ? (
                // Fallback to workflow_ids if workflows array is not available
                item.rawData.workflow_ids.map((workflowId: string) => (
                  <Card key={workflowId}>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-2">
                        <WorkflowIcon className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">Workflow</h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {workflowId}
                      </p>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <WorkflowIcon className="h-5 w-5 text-muted-foreground" />
                      <h3 className="font-medium">No Workflows</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      This agent is not associated with any workflows.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Versions Tab Content */}
        <TabsContent value="versions">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Version History</h2>
            <p className="text-muted-foreground mb-6">
              View all versions of this agent and their changes over time.
            </p>

            {versionsLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">
                  Loading versions...
                </span>
              </div>
            ) : versionsError ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
                <div className="flex items-center justify-between">
                  <p className="text-red-700 dark:text-red-400">
                    {versionsError}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchVersions(versionsPage)}
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Retry
                  </Button>
                </div>
              </div>
            ) : !item.rawData.source_agent_id ? (
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4 mb-6">
                <p className="text-amber-700 dark:text-amber-400">
                  No agent ID available for version history.
                </p>
              </div>
            ) : (
              <>
                {/* Versions Table */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  {/* Table Header */}
                  <div className="bg-muted dark:bg-gray-800 px-6 py-3 border-b border-border dark:border-gray-700">
                    <div className="grid grid-cols-12 gap-4 font-medium text-sm">
                      <div className="col-span-3">Version Number</div>
                      <div className="col-span-6">Changelog</div>
                      <div className="col-span-3">Date</div>
                    </div>
                  </div>

                  {/* Table Body */}
                  <div className="divide-y divide-border dark:divide-gray-700">
                    {versions.length > 0 ? (
                      versions.map((version) => (
                        <div
                          key={version.id}
                          className={`px-6 py-4 hover:bg-muted/50 transition-colors ${
                            version.is_current
                              ? "bg-primary/5 dark:bg-primary/10 border-l-4 border-l-primary"
                              : ""
                          }`}
                        >
                          <div className="grid grid-cols-12 gap-4 items-start">
                            {/* Version Number */}
                            <div className="col-span-3">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">
                                  {version.version_number}
                                </span>
                                {version.is_current && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    Current
                                  </Badge>
                                )}
                              </div>
                            </div>

                            {/* Changelog */}
                            <div className="col-span-6">
                              {version.version_notes ? (
                                <p className="text-sm text-muted-foreground">
                                  {version.version_notes.length > 200
                                    ? `${version.version_notes.slice(0, 200)}...`
                                    : version.version_notes}
                                </p>
                              ) : (
                                <span className="text-sm text-muted-foreground italic">
                                  No version notes provided
                                </span>
                              )}

                              {/* Tags if available */}
                              {/* {version.tags && version.tags.length > 0 && (
                                <div className="flex items-center gap-1 mt-2">
                                  <Tag className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-xs text-muted-foreground">
                                    {version.tags.join(", ")}
                                  </span>
                                </div>
                              )} */}
                            </div>

                            {/* Date */}
                            <div className="col-span-3">
                              {version.created_at ? (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Calendar className="h-4 w-4" />
                                  <span>
                                    {format(
                                      new Date(version.created_at),
                                      "MMM d, yyyy"
                                    )}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm text-muted-foreground italic">
                                  No date available
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="px-6 py-8 text-center">
                        <p className="text-muted-foreground">
                          No versions found for this agent.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Pagination */}
                {versionsTotalPages > 1 && (
                  <div className="flex justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newPage = Math.max(1, versionsPage - 1);
                        setVersionsPage(newPage);
                        fetchVersions(newPage);
                      }}
                      disabled={versionsPage === 1 || versionsLoading}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center px-4 text-sm text-muted-foreground">
                      Page {versionsPage} of {versionsTotalPages}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newPage = Math.min(
                          versionsTotalPages,
                          versionsPage + 1
                        );
                        setVersionsPage(newPage);
                        fetchVersions(newPage);
                      }}
                      disabled={
                        versionsPage === versionsTotalPages || versionsLoading
                      }
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </TabsContent>

        {/* Integration Tab Content */}
        <TabsContent value="integration">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">API Integration</h2>
            <p className="text-muted-foreground mb-4">
              Integrate this agent into your applications.
            </p>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Get your API Key</h3>
              <p className="text-muted-foreground mb-3">
                You&apos;ll need to sign in to the RUH Developer Portal and generate an API key to connect to this agent.
              </p>
              <Button
                variant="secondary"
                className="bg-gray-800 hover:bg-gray-700 text-white dark:bg-primary/90 dark:hover:bg-primary dark:text-white dark:border-primary/30"
                onClick={redirectToDeveloperPlatform}
              >
                Generate API Key
              </Button>
            </div>

            <div className="mt-8">
              <h3 className="text-lg font-medium mb-3">Code Example</h3>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm overflow-x-auto">
                  <code>{`// JavaScript Example
const sendMessage = async (message) => {
  try {
    const response = await axios.post('https://api.ruh.ai/agent/${item.id}/chat', {
      message: message
    }, {
      headers: {
        'Authorization': 'Bearer your-ruh-api-key',
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
  }
};`}</code>
                </pre>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </BaseDetailPage>
  );
}
