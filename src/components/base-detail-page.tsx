"use client";

import { LoginDialog } from "@/components/login-dialog";
import { SuccessDialog } from "@/components/success-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
// Input, Badge, Settings, Zap, useState, useEffect, EnvConfigKey removed
import { MarketplaceItem } from "@/data/marketplace-items";
import { useAddToRuh } from "@/hooks/use-add-to-ruh";
import { Calendar, ChevronLeft, Plus, Tag, User } from "lucide-react";
import { useRouter } from "next/navigation";
// useState, useEffect removed

// EnvConfigKey interface removed
// export interface EnvConfigKey {
//   key: string;
//   description: string;
// }

interface BaseDetailPageProps {
  item: MarketplaceItem;
  relatedItems?: MarketplaceItem[];
  children: React.ReactNode;
  refetch?: () => void;
  // onMcpConnectionChange removed
}

export function BaseDetailPage({
  item,
  children,
  refetch,
  // onMcpConnectionChange removed
}: BaseDetailPageProps) {
  const router = useRouter();
  // envValues, isMcpConnected state and related handlers/effects removed

  // Use our custom hook for adding to RUH
  const {
    addToRuh,
    isAddingToRuh,
    showLoginDialog,
    closeLoginDialog,
    showSuccessDialog,
    closeSuccessDialog,
    successData
  } = useAddToRuh({ refetch });

  const typeColor = {
    MCP: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
    AGENT: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    WORKFLOW: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  };

  const handleAdd = () => {
    // Call our hook to add the item to RUH
    addToRuh(item.id, item.type);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Login Dialog */}
      <LoginDialog isOpen={showLoginDialog} onOpenChange={closeLoginDialog} />

      {/* Success Dialog */}
      <SuccessDialog
        isOpen={showSuccessDialog}
        onOpenChange={closeSuccessDialog}
        itemId={successData?.itemId}
        itemType={successData?.itemType || item.type}
      />

      {/* Back button */}
      <Button
        variant="ghost"
        className="mb-6 pl-0 hover:bg-transparent"
        onClick={() => router.push('/')}
      >
        <ChevronLeft className="mr-2 h-4 w-4" />
        Back to Marketplace
      </Button>

      <div className="grid gap-25 md:grid-cols-3">
        {/* Main content */}
        <div className="md:col-span-2">
          {/* Item header and image in flex layout */}
          <div className="flex flex-col md:flex-row gap-6 mb-8">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${typeColor[item.type]}`}>
                  {item.type}
                </span>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-yellow-500">★</span>
                  <span className="ml-1 text-sm font-medium">{item.rating || item.average_rating ? item.rating.toFixed(1): 0}</span>
                  <span className="ml-1 text-sm text-muted-foreground">({item.use_count?.toLocaleString() || 0} uses)</span>
                </div>
              </div>
              <h1 className="text-3xl font-bold">{item.title}</h1>
              {/* <p className="text-muted-foreground mt-2">{item.description}</p> */}
            </div>
          </div>

          {/* Tab content - Passed as children */}
          {children}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Add button */}
          <Card>
            <CardContent className="pt-6"> {/* Ensure padding consistent with other cards */}
              <Button
                className="w-full"
                size="lg"
                onClick={handleAdd}
                disabled={isAddingToRuh || item.is_added}
              >
                {isAddingToRuh ? (
                  <>
                    <span className="animate-pulse">Adding to RUH...</span>
                  </>
                ) : item.is_added ? (
                  <>
                    <span>Item already added</span>
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Add to RUH
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* MCP Environment Configuration Card removed from here */}

          {/* Item Details */}
          <Card>
            <CardContent className="pt-6">
              <h3 className="font-medium mb-4">Details</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-2">
                  <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Author:</p>
                    <p className="text-sm text-muted-foreground">{item.owner_name || "RUH AI"}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Released:</p>
                    <p className="text-sm text-muted-foreground">
                      {item.rawData?.created_at
                        ? new Date(item.rawData.created_at).toLocaleDateString()
                        : "15/01/2024"}
                    </p>
                  </div>
                </div>

                {item.category && (
                  <div className="flex items-start gap-2">
                    <Tag className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Category:</p>
                      <p className="text-sm text-muted-foreground">{item.category}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* MCP Server Connection Card REMOVED */}
        </div>
      </div>
    </div>
  );
}
