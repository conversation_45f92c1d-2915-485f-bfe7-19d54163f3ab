"use client";

import { Suspense } from "react";
import { Footer } from "@/components/footer";
import { MarketplaceItemCard } from "@/components/marketplace-item-card";
import {
  ItemSkeletonGrid,
  AgentItemSkeletonGrid,
  WorkflowItemSkeletonGrid
} from "@/components/marketplace/item-skeleton";
import { ErrorState } from "@/components/ui/error-state";
import { SearchHeader } from "@/components/search-header";
import { useMarketplaceSearch } from "@/hooks/useMarketplaceSearch";
import { MCP, Agent, Workflow } from "@/lib/api/types/marketplace-items";

interface DynamicMarketplacePageProps {
  defaultTab?: string;
}

// This component uses useSearchParams and will be wrapped in Suspense
function DynamicMarketplaceContent({ defaultTab = "agents" }: DynamicMarketplacePageProps) {
  // Use our custom hook for search functionality
  const {
    items,
    loading,
    error,
    searchQuery,
    tagsFilter,
    currentTab,
    currentCategory,
    handleSearch,
    handleTabChange,
    handleCategoryChange,
    clearFilters
  } = useMarketplaceSearch({ defaultTab });

  console.log({items});

  return (
    <>
      <SearchHeader
        title="Discover & Deploy"
        subtitle="Advanced AI Solutions"
        initialQuery={searchQuery}
        initialTags={tagsFilter}
        onSearch={handleSearch}
        currentTab={currentTab}
        currentCategory={currentCategory}
        onTabChange={handleTabChange}
        onCategoryChange={handleCategoryChange}
        onClearFilters={clearFilters}
      />
      <main className="min-h-screen bg-background">
        <div className="container py-4 md:py-6">
          <div className="flex flex-col gap-6">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold">
                {currentTab === "agents" ? "Specialized Agents" :
                 currentTab === "mcps" ? "MCP Servers" :
                 currentTab === "workflows" ? "AI Workflows" :
                 "All Marketplace Items"}
              </h1>
              <span className="text-sm text-muted-foreground">{items.length} items</span>
            </div>

            {loading ? (
              <>
                {currentTab === "agents" && <AgentItemSkeletonGrid count={8} />}
                {currentTab === "mcps" && <ItemSkeletonGrid count={8} />}
                {currentTab === "workflows" && <WorkflowItemSkeletonGrid count={8} />}
                {currentTab === "all" && <ItemSkeletonGrid count={8} />}
              </>
            ) : error ? (
              <ErrorState
                title={`Failed to load ${currentTab.toUpperCase()}`}
                message={`We couldn't load the ${currentTab} at this time. Please try again later.`}
                onRetry={() => handleTabChange(currentTab)}
              />
            ) : items.length === 0 ? (
              <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                <h3 className="text-xl font-semibold">No items found</h3>
                <p className="mt-2 text-muted-foreground">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            ) : (
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {items.map((item: MCP | Agent | Workflow) => {
                  // Determine the type based on the current tab or item_type
                  let type;
                  if (currentTab !== "all") {
                    type = currentTab === "mcps" ? "MCP" :
                           currentTab === "agents" ? "AGENT" :
                           "WORKFLOW";
                  } else {
                    // For "all" tab, use the item's type
                    type = item.item_type ||
                           ('agent_category' in item ? "AGENT" :
                            'execution_count' in item ? "WORKFLOW" : "MCP");
                  }

                  return (
                    <MarketplaceItemCard
                      key={item.id}
                      id={item.id}
                      title={item.name}
                      description={item.description}
                      type={type as "MCP" | "AGENT" | "WORKFLOW"}
                      rating={item.rating || 4.9}
                      use_count={item.use_count || item.downloads || 0}
                      tags={item.tags}
                      avatar={ item.avatar ? item.avatar : undefined}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}

// Main component that wraps DynamicMarketplaceContent in a Suspense boundary
export function DynamicMarketplacePage({ defaultTab = "agents" }: DynamicMarketplacePageProps) {
  return (
    <Suspense fallback={
      <>
        <SearchHeader
          title="Discover & Deploy"
          subtitle="Advanced AI Solutions"
          currentTab={defaultTab}
          currentCategory="all"
          onSearch={() => {}}
          onTabChange={() => {}}
          onCategoryChange={() => {}}
        />
        <main className="min-h-screen bg-background">
          <div className="container py-4 md:py-6">
            <div className="flex flex-col gap-6">
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold">
                  {defaultTab === "agents" ? "Specialized Agents" :
                   defaultTab === "mcps" ? "MCP Servers" :
                   "AI Workflows"}
                </h1>
              </div>
              {defaultTab === "agents" && <AgentItemSkeletonGrid count={8} />}
              {defaultTab === "mcps" && <ItemSkeletonGrid count={8} />}
              {defaultTab === "workflows" && <WorkflowItemSkeletonGrid count={8} />}
            </div>
          </div>
        </main>
        <Footer />
      </>
    }>
      <DynamicMarketplaceContent defaultTab={defaultTab} />
    </Suspense>
  );
}
