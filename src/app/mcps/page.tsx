'use client'
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function MCPsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the home page with the mcps tab selected via query parameter
    router.replace('/?tab=mcps');
  }, [router]);

  // Return a loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <p className="text-lg">Redirecting to MCPs...</p>
    </div>
  );
}
