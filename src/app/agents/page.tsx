'use client'
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AgentsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the home page with the agents tab selected via query parameter
    router.replace('/?tab=agents');
  }, [router]);

  // Return a loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <p className="text-lg">Redirecting to agents...</p>
    </div>
  );
}
