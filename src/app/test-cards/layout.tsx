import { Metadata } from 'next';
import Link from 'next/link';
import { TestNav } from './test-nav';

export const metadata: Metadata = {
  title: 'Test Cards - RUH Marketplace',
  description: 'Test page for marketplace item cards',
};

export default function TestCardsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <Link href="/" className="mr-6 flex items-center space-x-2">
              <span className="font-bold">RUH Marketplace</span>
            </Link>
            <TestNav />
          </div>
        </div>
      </header>
      <main className="flex-1">{children}</main>
    </div>
  );
}
