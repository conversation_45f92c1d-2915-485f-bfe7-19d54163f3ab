'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';

export function TestNav() {
  const pathname = usePathname();

  return (
    <nav className="flex items-center space-x-6 text-sm font-medium">
      <Link href="/test-cards">
        <Button variant={pathname === '/test-cards' ? 'default' : 'ghost'}>
          API Data Cards
        </Button>
      </Link>
      <Link href="/test-cards/simple">
        <Button variant={pathname === '/test-cards/simple' ? 'default' : 'ghost'}>
          Simple Mock Cards
        </Button>
      </Link>
      <Link href="/">
        <Button variant="ghost">
          Back to Home
        </Button>
      </Link>
    </nav>
  );
}
