'use client';

import { useState } from 'react';
import { MarketplaceItemCard } from '@/components/marketplace-item-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MarketplaceItem } from '@/data/marketplace-items';

export default function CustomTestCardPage() {
  // Default values for the card
  const [cardProps, setCardProps] = useState<MarketplaceItem & {
    department?: string;
    tone?: string;
    model_provider?: string;
    model_name?: string;
    workflow_ids?: string[];
    mcp_server_ids?: string[];
    created_at?: string;
    updated_at?: string;
  }>({
    id: 'test-item-1',
    title: 'Test Marketplace Item',
    description: 'This is a test marketplace item for UI testing purposes. You can customize all the properties to see how the card looks with different data.',
    category: 'Testing',
    owner_name: 'Test User',
    rating: 4.5,
    use_count: 1000,
    imageSrc: '/assets/mcp.png',
    type: 'MCP',
    department: 'Engineering',
    tone: 'Professional',
    model_provider: 'OpenAI',
    model_name: 'GPT-4',
    workflow_ids: ['workflow-1', 'workflow-2'],
    mcp_server_ids: ['mcp-1', 'mcp-2'],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });

  // Handle input changes
  const handleChange = (field: string, value: string | number | boolean | string[]) => {
    setCardProps(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle array input changes (comma-separated values)
  const handleArrayChange = (field: string, value: string) => {
    const arrayValue = value.split(',').map(item => item.trim());
    setCardProps(prev => ({
      ...prev,
      [field]: arrayValue
    }));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Custom Card Tester</h1>
        <p className="text-gray-500">
          Customize the marketplace item card properties to test different configurations
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Form for customizing the card */}
        <Card>
          <CardHeader>
            <CardTitle>Card Properties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={cardProps.title}
                onChange={(e) => handleChange('title', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={cardProps.description}
                onChange={(e) => handleChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Input
                  id="category"
                  value={cardProps.category}
                  onChange={(e) => handleChange('category', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="owner_name">Author</Label>
                <Input
                  id="owner_name"
                  value={cardProps.owner_name}
                  onChange={(e) => handleChange('owner_name', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  value={cardProps.type}
                  onValueChange={(value) => handleChange('type', value)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MCP">MCP</SelectItem>
                    <SelectItem value="AGENT">AGENT</SelectItem>
                    <SelectItem value="WORKFLOW">WORKFLOW</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="imageSrc">Image Source</Label>
                <Select
                  value={cardProps.imageSrc}
                  onValueChange={(value) => handleChange('imageSrc', value)}
                >
                  <SelectTrigger id="imageSrc">
                    <SelectValue placeholder="Select image" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="/assets/mcp.png">MCP Image</SelectItem>
                    <SelectItem value="/assets/agent.png">Agent Image</SelectItem>
                    <SelectItem value="/assets/workflow.png">Workflow Image</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="rating">Rating (0-5)</Label>
              <Input
                id="rating"
                type="number"
                min={0}
                max={5}
                step={0.1}
                value={cardProps.rating}
                onChange={(e) => handleChange('rating', parseFloat(e.target.value))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="use_count">Usage Count</Label>
              <Input
                id="use_count"
                type="number"
                min={0}
                max={10000}
                step={100}
                value={cardProps.use_count}
                onChange={(e) => handleChange('use_count', parseInt(e.target.value))}
              />
            </div>



            {cardProps.type === 'AGENT' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      value={cardProps.department}
                      onChange={(e) => handleChange('department', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tone">Tone</Label>
                    <Input
                      id="tone"
                      value={cardProps.tone}
                      onChange={(e) => handleChange('tone', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="model_provider">Model Provider</Label>
                    <Input
                      id="model_provider"
                      value={cardProps.model_provider}
                      onChange={(e) => handleChange('model_provider', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="model_name">Model Name</Label>
                    <Input
                      id="model_name"
                      value={cardProps.model_name}
                      onChange={(e) => handleChange('model_name', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="workflow_ids">Workflow IDs (comma-separated)</Label>
                  <Input
                    id="workflow_ids"
                    value={cardProps.workflow_ids?.join(', ')}
                    onChange={(e) => handleArrayChange('workflow_ids', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mcp_server_ids">MCP Server IDs (comma-separated)</Label>
                  <Input
                    id="mcp_server_ids"
                    value={cardProps.mcp_server_ids?.join(', ')}
                    onChange={(e) => handleArrayChange('mcp_server_ids', e.target.value)}
                  />
                </div>
              </>
            )}

            <Button
              onClick={() => setCardProps({
                id: 'test-item-1',
                title: 'Test Marketplace Item',
                description: 'This is a test marketplace item for UI testing purposes. You can customize all the properties to see how the card looks with different data.',
                category: 'Testing',
                owner_name: 'Test User',
                rating: 4.5,
                use_count: 1000,
                imageSrc: '/assets/mcp.png',
                type: 'MCP',
                department: 'Engineering',
                tone: 'Professional',
                model_provider: 'OpenAI',
                model_name: 'GPT-4',
                workflow_ids: ['workflow-1', 'workflow-2'],
                mcp_server_ids: ['mcp-1', 'mcp-2'],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })}
              variant="outline"
            >
              Reset to Default
            </Button>
          </CardContent>
        </Card>

        {/* Card preview */}
        <div className="flex flex-col space-y-4">
          <h2 className="text-xl font-semibold">Card Preview</h2>
          <div className="max-w-sm mx-auto">
            <MarketplaceItemCard {...cardProps} />
          </div>
        </div>
      </div>
    </div>
  );
}
