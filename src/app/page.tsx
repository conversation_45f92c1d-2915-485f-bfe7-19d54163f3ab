'use client';

import { Foot<PERSON> } from "@/components/footer";
import { MarketplaceItemCard } from "@/components/marketplace-item-card";
import {
  AgentItemSkeletonGrid,
  ItemSkeletonGrid,
  WorkflowItemSkeletonGrid
} from "@/components/marketplace/item-skeleton";
import { SectionHeader } from "@/components/section-header";
import { ErrorState } from "@/components/ui/error-state";
import { Button } from "@/components/ui/button";
import { api } from '@/lib/api';
import { MarketplaceItem } from "@/lib/api/types/marketplace-items";
import { useRouter } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import { SearchBar } from "@/components/searchbar";

// Static fallback component
function HomePageFallback() {
  return (
    <div className="flex flex-col min-h-screen">
      <SearchBar />
      <section className="py-8 md:py-12 flex-grow flex flex-col">
        <div className="container flex flex-col h-full">
          <SectionHeader title="Popular Agents" />
          <AgentItemSkeletonGrid count={10} />
          <SectionHeader title="Popular Workflows" />
          <WorkflowItemSkeletonGrid count={10} />
          <SectionHeader title="Popular MCPs" />
          <ItemSkeletonGrid count={10} />
        </div>
      </section>
      <Footer />
    </div>
  );
}

// Client component with new design
function HomeContent() {
  const router = useRouter();

  // State for API data
  const [agents, setAgents] = useState<MarketplaceItem[]>([]);
  const [mcps, setMcps] = useState<MarketplaceItem[]>([]);
  const [workflows, setWorkflows] = useState<MarketplaceItem[]>([]);

  // Loading states
  const [agentsLoading, setAgentsLoading] = useState(true);
  const [mcpsLoading, setMcpsLoading] = useState(true);
  const [workflowsLoading, setWorkflowsLoading] = useState(true);

  // Error states
  const [agentsError, setAgentsError] = useState<Error | null>(null);
  const [mcpsError, setMcpsError] = useState<Error | null>(null);
  const [workflowsError, setWorkflowsError] = useState<Error | null>(null);

  // Function to fetch popular agents
  const fetchPopularAgents = async () => {
    setAgentsLoading(true);
    setAgentsError(null);

    try {
      const response = await api.agent.getAgents({
        page: 1,
        page_size: 8
      });

      console.log('Popular agents response:', response);

      if (response.data) {
        const responseData = response.data as any;
        let items: MarketplaceItem[] = [];

        if (responseData.data && Array.isArray(responseData.data)) {
          items = responseData.data;
        } else if (responseData.templates && Array.isArray(responseData.templates)) {
          items = responseData.templates;
        } else if (responseData.data && responseData.data.data && Array.isArray(responseData.data.data)) {
          items = responseData.data.data;
        } else if (responseData.data && responseData.data.templates && Array.isArray(responseData.data.templates)) {
          items = responseData.data.templates;
        }

        setAgents(items);
      }
    } catch (err) {
      console.error('Error fetching popular agents:', err);
      setAgentsError(err instanceof Error ? err : new Error('Failed to fetch popular agents'));
      setAgents([]);
    } finally {
      setAgentsLoading(false);
    }
  };

  // Function to fetch popular workflows
  const fetchPopularWorkflows = async () => {
    setWorkflowsLoading(true);
    setWorkflowsError(null);

    try {
      const response = await api.workflow.getWorkflows({
        page: 1,
        page_size: 8
      });

      console.log('Popular workflows response:', response);

      if (response.data) {
        const responseData = response.data as any;
        let items: MarketplaceItem[] = [];

        if (responseData.data && Array.isArray(responseData.data)) {
          items = responseData.data;
        } else if (responseData.templates && Array.isArray(responseData.templates)) {
          items = responseData.templates;
        } else if (responseData.data && responseData.data.data && Array.isArray(responseData.data.data)) {
          items = responseData.data.data;
        } else if (responseData.data && responseData.data.templates && Array.isArray(responseData.data.templates)) {
          items = responseData.data.templates;
        }

        setWorkflows(items);
      }
    } catch (err) {
      console.error('Error fetching popular workflows:', err);
      setWorkflowsError(err instanceof Error ? err : new Error('Failed to fetch popular workflows'));
      setWorkflows([]);
    } finally {
      setWorkflowsLoading(false);
    }
  };

  // Function to fetch popular MCPs
  const fetchPopularMCPs = async () => {
    setMcpsLoading(true);
    setMcpsError(null);

    try {
      const response = await api.mcp.getMCPs({
        page: 1,
        page_size: 8
      });

      console.log('Popular MCPs response:', response);

      if (response.data) {
        const responseData = response.data as any;
        let items: MarketplaceItem[] = [];

        if (responseData.data && Array.isArray(responseData.data)) {
          items = responseData.data;
        } else if (responseData.templates && Array.isArray(responseData.templates)) {
          items = responseData.templates;
        } else if (responseData.data && responseData.data.data && Array.isArray(responseData.data.data)) {
          items = responseData.data.data;
        } else if (responseData.data && responseData.data.templates && Array.isArray(responseData.data.templates)) {
          items = responseData.data.templates;
        }

        setMcps(items);
      }
    } catch (err) {
      console.error('Error fetching popular MCPs:', err);
      setMcpsError(err instanceof Error ? err : new Error('Failed to fetch popular MCPs'));
      setMcps([]);
    } finally {
      setMcpsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchPopularAgents();
    fetchPopularWorkflows();
    fetchPopularMCPs();
  }, []);

  // Helper function to render section with view all button
  const renderSection = (
    title: string,
    items: MarketplaceItem[],
    loading: boolean,
    error: Error | null,
    viewAllRoute: string,
    itemType: "AGENT" | "MCP" | "WORKFLOW"
  ) => {
    const imageSrc = itemType === "AGENT" ? "/assets/agent.png" :
                    itemType === "MCP" ? "/assets/mcp.png" :
                    "/assets/workflow.png";

    return (
      <div className="mb-12">
        <div className="flex items-start justify-between mb-6">
          <SectionHeader title={title} />
          <Button
            variant="outline"
            onClick={() => router.push(viewAllRoute)}
            className="text-purple-600 border-purple-600 hover:bg-purple-50"
          >
            View all {title.toLowerCase()} →
          </Button>
        </div>

        {loading ? (
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {itemType === "AGENT" && <AgentItemSkeletonGrid count={10} />}
            {itemType === "WORKFLOW" && <WorkflowItemSkeletonGrid count={10} />}
            {itemType === "MCP" && <ItemSkeletonGrid count={10} />}
          </div>
        ) : error ? (
          <ErrorState
            title={`Failed to load ${title}`}
            message={`We couldn't load the ${title.toLowerCase()} at this time. Please try again later.`}
            onRetry={() => {
              if (itemType === "AGENT") fetchPopularAgents();
              else if (itemType === "WORKFLOW") fetchPopularWorkflows();
              else fetchPopularMCPs();
            }}
          />
        ) : items.length === 0 ? (
          <div className="text-center py-8">
            <p>No {title.toLowerCase()} found.</p>
          </div>
        ) : (
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {items.map((item: MarketplaceItem) => (
              <MarketplaceItemCard
                key={item.id}
                id={item.id}
                title={item.name}
                description={item.description}
                category={item.category || "General"}
                owner_name={item.owner_name}
                imageSrc={imageSrc}
                type={itemType}
                rating={item.average_rating || 0}
                use_count={item.use_count || 0}
                tags={item.tags && Array.isArray(item.tags) ? item.tags : []}
                avatar={item.avatar || undefined}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* New SearchBar Component */}
      <SearchBar />

      {/* Main Content */}
      <section className="py-8 md:py-12 flex-grow">
        <div className="container">
          {/* Popular Agents Section */}
          {renderSection("Popular Agents", agents, agentsLoading, agentsError, "/agents", "AGENT")}

          {/* Popular Workflows Section */}
          {renderSection("Popular Workflows", workflows, workflowsLoading, workflowsError, "/workflows", "WORKFLOW")}

          {/* Popular MCPs Section */}
          {renderSection("Popular MCPs", mcps, mcpsLoading, mcpsError, "/mcps", "MCP")}
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default function Home() {
  return (
    <Suspense fallback={<HomePageFallback />}>
      <HomeContent />
    </Suspense>
  );
}