'use client';

import { Footer } from "@/components/footer";
import { MarketplaceItemCard } from "@/components/marketplace-item-card";
import {
  AgentItemSkeletonGrid,
  ItemSkeletonGrid,
  WorkflowItemSkeletonGrid
} from "@/components/marketplace/item-skeleton";
import { Pagination } from "@/components/pagination";
import { SearchHeader } from "@/components/search-header";
import { SectionHeader } from "@/components/section-header";
import { ErrorState } from "@/components/ui/error-state";
import { api } from '@/lib/api';
import { MarketplaceItem } from "@/lib/api/types/marketplace-items";
import { Suspense, useEffect, useState } from 'react';

// Static fallback component without useSearchParams()
function HomePageFallback() {
  return (
    <div className="flex flex-col min-h-screen">
      <SearchHeader
        title="Discover & Deploy"
        subtitle="Advanced AI Solutions"
        currentTab="all"
        onSearch={() => {}}
        onTabChange={() => {}}
        onCategoryChange={() => {}}
      />
      <section className="py-8 md:py-12 flex-grow flex flex-col">
        <div className="container flex flex-col h-full">
          <SectionHeader title="Popular Marketplace Items" />
          <ItemSkeletonGrid count={8} />
        </div>
      </section>
      <Footer />
    </div>
  );
}

// Client component with search functionality
function HomeContent() {
  // State for search parameters
  const [currentTab, setCurrentTab] = useState("all");
  const [currentCategory, setCurrentCategory] = useState("all");

  // State for API data
  const [items, setItems] = useState<MarketplaceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // State for categorized items for "All" tab
  const [agents, setAgents] = useState<MarketplaceItem[]>([]);
  const [mcps, setMcps] = useState<MarketplaceItem[]>([]);
  const [workflows, setWorkflows] = useState<MarketplaceItem[]>([]);

  // State for search parameters
  const [searchQuery, setSearchQuery] = useState("");
  const [tagsFilter, setTagsFilter] = useState("");

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPrevPage, setHasPrevPage] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Function to fetch data based on the current tab
  const fetchData = async (tab: string, category: string = 'all', search?: string, tags?: string, page: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      let response;

      // Prepare API parameters
      const apiParams = {
        page,
        page_size: itemsPerPage,
        category: category !== 'all' ? category : undefined,
        search: search || undefined,
        tags: tags || undefined,
        // Sort all tabs by most popular
        sort_by: 'MOST_POPULAR'
      };

      console.log(`Fetching ${tab} with params:`, apiParams);

      // Fetch data based on the current tab
      let items: MarketplaceItem[];
      let metadata: any;

      switch (tab) {
        case 'mcps':
          response = await api.mcp.getMCPs(apiParams);
          break;
        case 'agents':
          response = await api.agent.getAgents(apiParams);
          break;
        case 'workflows':
          response = await api.workflow.getWorkflows(apiParams);
          break;
        case 'all':
        default:
          response = await api.combinedMarketplace.getCombinedItems(apiParams);
          break;
      }

      console.log(`Raw ${tab} endpoint response:`, response);

      if (response.data) {
        // Use type assertion to handle the response data more flexibly
        const responseData = response.data as any;
        
        // Check for different response structures
        if (responseData.data && Array.isArray(responseData.data)) {
          // New format with data array and metadata
          items = responseData.data;
          metadata = responseData.metadata || {
            total: items.length,
            page: currentPage,
            total_pages: Math.ceil(items.length / itemsPerPage),
            has_next: false,
            has_prev: false
          };
        } else if (responseData.templates && Array.isArray(responseData.templates)) {
          // Legacy format with templates array
          items = responseData.templates;
          metadata = {
            total: responseData.total || items.length,
            page: responseData.page || currentPage,
            total_pages: responseData.total_pages || Math.ceil(items.length / itemsPerPage),
            has_next: responseData.page < responseData.total_pages,
            has_prev: responseData.page > 1
          };
        } else if (responseData.data && responseData.data.data && Array.isArray(responseData.data.data)) {
          // Nested data structure (seen in agents response)
          items = responseData.data.data;
          metadata = responseData.data.metadata || {
            total: items.length,
            page: currentPage,
            total_pages: Math.ceil(items.length / itemsPerPage),
            has_next: false,
            has_prev: false
          };
        } else if (responseData.data && responseData.data.templates && Array.isArray(responseData.data.templates)) {
          // Nested templates structure (seen in agents response)
          items = responseData.data.templates;
          metadata = {
            total: responseData.data.total || items.length,
            page: responseData.data.page || currentPage,
            total_pages: responseData.data.total_pages || Math.ceil(items.length / itemsPerPage),
            has_next: (responseData.data.page || 1) < (responseData.data.total_pages || 1),
            has_prev: (responseData.data.page || 1) > 1
          };
        } else {
          console.error('Unexpected response format:', response);
          throw new Error('Unexpected response format');
        }

        setItems(items);

        // Categorize items if currentTab is "all"
        if (tab === "all") {
          setAgents(items.filter(item => item.item_type === "AGENT"));
          setMcps(items.filter(item => item.item_type === "MCP"));
          setWorkflows(items.filter(item => item.item_type === "WORKFLOW"));
        } else {
          // Clear categorized items when not on "all" tab
          setAgents([]);
          setMcps([]);
          setWorkflows([]);
        }

        setTotalItems(metadata.total);
        setTotalPages(metadata.total_pages);
        setCurrentPage(metadata.page);
        setHasNextPage(metadata.has_next);
        setHasPrevPage(metadata.has_prev);
        setItemsPerPage(apiParams.page_size);
      } else {
        console.error('Unexpected response format:', response);
        throw new Error('Unexpected response format');
      }
    } catch (err) {
      console.error(`Error fetching ${tab}:`, err);
      setError(err instanceof Error ? err : new Error(`Failed to fetch ${tab}`));
      setItems([]);
      setTotalPages(1);

      // Clear categorized items on error
      setAgents([]);
      setMcps([]);
      setWorkflows([]);
    } finally {
      setLoading(false);
    }
  };

  // Read tab and search params from URL on component mount
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab');
      const searchParam = urlParams.get('search');
      const tagsParam = urlParams.get('tags');
      const categoryParam = urlParams.get('category');

      // If a valid tab is specified in the URL, set it as the current tab
      if (tabParam && ['all', 'agents', 'mcps', 'workflows'].includes(tabParam)) {
        setCurrentTab(tabParam);
      }

      // Set category if present in URL
      if (categoryParam) {
        setCurrentCategory(categoryParam);
      }

      // Set search query if present in URL
      if (searchParam) {
        setSearchQuery(searchParam);
      }

      // Set tags filter if present in URL
      if (tagsParam) {
        setTagsFilter(tagsParam);
      }
    }
  }, []);

  // Fetch data on component mount or when tab/category/search/page changes
  useEffect(() => {
    fetchData(currentTab, currentCategory, searchQuery, tagsFilter, currentPage);
  }, [currentTab, currentCategory, searchQuery, tagsFilter, currentPage]);

  // Function to handle tab change
  const handleTabChange = (tab: string) => {
    setCurrentTab(tab);
    setCurrentPage(1); // Reset to first page when changing tabs

    // Update URL with the new tab
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      if (tab === 'all') {
        url.searchParams.delete('tab');
      } else {
        url.searchParams.set('tab', tab);
      }

      // Preserve search parameters
      if (searchQuery) {
        url.searchParams.set('search', searchQuery);
      }

      if (tagsFilter) {
        url.searchParams.set('tags', tagsFilter);
      }

      // Update URL without full navigation
      window.history.pushState({}, '', url.toString());
    }

    // fetchData will be called automatically due to the useEffect dependency
  };

  // Function to handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0); // Scroll to top when changing pages
  };

  // Function to handle category change
  const handleCategoryChange = (category: string) => {
    setCurrentCategory(category);

    // Update URL with the new category
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      if (category === 'all') {
        url.searchParams.delete('category');
      } else {
        url.searchParams.set('category', category);
      }

      // Update URL without full navigation
      window.history.pushState({}, '', url.toString());
    }

    // fetchData will be called automatically due to the useEffect dependency
  };

  // Function to handle search
  const handleSearch = (query: string, tags?: string) => {
    console.log("Search query:", query, "Tags:", tags);

    // Update state
    setSearchQuery(query);
    setTagsFilter(tags || "");

    // Update URL with search parameters
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      if (query) {
        url.searchParams.set('search', query);
      } else {
        url.searchParams.delete('search');
      }

      if (tags) {
        url.searchParams.set('tags', tags);
      } else {
        url.searchParams.delete('tags');
      }

      // Update URL without full navigation
      window.history.pushState({}, '', url.toString());
    }

    // Fetch data with search parameters
    fetchData(currentTab, currentCategory, query, tags);
  };

  // Function to clear filters
  const clearFilters = () => {
    // Reset category to 'all'
    setCurrentCategory('all');
    // Clear search parameters
    setSearchQuery('');
    setTagsFilter('');

    // Update URL to remove all search parameters
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('search');
      url.searchParams.delete('tags');
      url.searchParams.delete('category');

      // Keep the tab parameter if it exists
      if (currentTab !== 'all') {
        url.searchParams.set('tab', currentTab);
      } else {
        url.searchParams.delete('tab');
      }

      // Update URL without full navigation
      window.history.pushState({}, '', url.toString());
    }

    // fetchData will be called automatically due to the useEffect dependency
    console.log("Clearing filters");
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Search Header */}
      <SearchHeader
        title="Discover & Deploy"
        subtitle="Advanced AI Solutions"
        currentTab={currentTab}
        currentCategory={currentCategory}
        initialQuery={searchQuery}
        initialTags={tagsFilter}
        onTabChange={handleTabChange}
        onCategoryChange={handleCategoryChange}
        onSearch={handleSearch}
        onClearFilters={clearFilters}
      />

      {/* Conditional rendering based on selected tab */}
      <section className="py-8 md:py-12 flex-grow flex flex-col">
        <div className="container flex flex-col h-full">
          <SectionHeader
            title={
              currentTab === "agents" ? "Most Popular Agents" :
              currentTab === "mcps" ? "Most Popular MCPs" :
              currentTab === "workflows" ? "Most Popular Workflows" :
              ""
            }
          />

          {loading ? (
            <div className="flex-grow overflow-y-auto max-h-[500px] mt-5">
              {currentTab === "agents" && <AgentItemSkeletonGrid count={itemsPerPage} />}
              {currentTab === "mcps" && <ItemSkeletonGrid count={itemsPerPage} />}
              {currentTab === "workflows" && <WorkflowItemSkeletonGrid count={itemsPerPage} />}
              {currentTab === "all" && <ItemSkeletonGrid count={itemsPerPage} />}
            </div>
          ) : error ? (
            <div className="flex-grow overflow-y-auto max-h-[500px]">
              <ErrorState
                title={`Failed to load ${currentTab.toUpperCase()}`}
                message={`We couldn't load the ${currentTab} at this time. Please try again later.`}
                onRetry={() => fetchData(currentTab, currentCategory, searchQuery, tagsFilter, currentPage)}
              />
            </div>
          ) : items.length === 0 ? (
            <div className="flex-grow overflow-y-auto text-center py-8 min-h-[200px] max-h-[500px]">
              <p>No items found. Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="flex flex-col h-full">
              {currentTab === "all" ? (
                <>
                  {/* Agents Section */}
                  {agents.length > 0 && (
                    <div className="mb-8">
                      <SectionHeader title="Most Popular Agents" className="mb-4" />
                      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 overflow-y-auto max-h-[500px]">
                        {agents.map((item: MarketplaceItem) => (
                          <MarketplaceItemCard
                            key={item.id}
                            id={item.id}
                            title={item.name}
                            description={item.description}
                            category={item.category || "General"}
                            owner_name={item.owner_name}
                            imageSrc="/assets/agent.png"
                            type="AGENT"
                            rating={item.average_rating || 0}
                            use_count={item.use_count || 0}
                            tags={item.tags && Array.isArray(item.tags) ? item.tags : []}
                            avatar={item.avatar || undefined}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* MCPs Section */}
                  {mcps.length > 0 && (
                    <div className="mb-8">
                      <SectionHeader title="Most Popular MCPs" className="mb-4" />
                      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 overflow-y-auto max-h-[500px]">
                        {mcps.map((item: MarketplaceItem) => (
                          <MarketplaceItemCard
                            key={item.id}
                            id={item.id}
                            title={item.name}
                            description={item.description}
                            category={item.category || "General"}
                            owner_name={item.owner_name}
                            imageSrc="/assets/mcp.png"
                            type="MCP"
                            rating={item.average_rating || 0}
                            use_count={item.use_count || 0}
                            tags={item.tags && Array.isArray(item.tags) ? item.tags : []}
                            avatar={item.avatar || undefined}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Workflows Section */}
                  {workflows.length > 0 && (
                    <div className="mb-8 mt-5">
                      <SectionHeader title="Most Popular Workflows" className="mb-4" />
                      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 overflow-y-auto max-h-[500px]">
                        {workflows.map((item: MarketplaceItem) => (
                          <MarketplaceItemCard
                            key={item.id}
                            id={item.id}
                            title={item.name}
                            description={item.description}
                            category={item.category || "General"}
                            owner_name={item.owner_name}
                            imageSrc="/assets/workflow.png"
                            type="WORKFLOW"
                            rating={item.average_rating || 0}
                            use_count={item.use_count || 0}
                            tags={item.tags && Array.isArray(item.tags) ? item.tags : []}
                            avatar={item.avatar || undefined}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 overflow-y-auto flex-grow max-h-[500px]">
                  {items.map((item: MarketplaceItem) => {
                    // Debug log to see the item and its type
                    console.log("Item:", item);
                    console.log("Item type:", item.item_type);
                    
                    // Determine the type based on the current tab or item_type
                    let type: "MCP" | "AGENT" | "WORKFLOW";
                    if (currentTab !== "all") {
                      type = currentTab === "mcps" ? "MCP" :
                             currentTab === "agents" ? "AGENT" :
                             "WORKFLOW";
                    } else {
                      // For "all" tab, use the item's type
                      type = (item.item_type as "MCP" | "AGENT" | "WORKFLOW") ||
                             ('agent_category' in item ? "AGENT" :
                              'execution_count' in item ? "WORKFLOW" : "MCP");
                    }

                    // Determine image source based on type
                    const imageSrc = type === "AGENT" ? "/assets/agent.png" :
                                    type === "MCP" ? "/assets/mcp.png" :
                                    "/assets/workflow.png";

                    return (
                      <MarketplaceItemCard
                        key={item.id}
                        id={item.id}
                        title={item.name}
                        description={item.description}
                        category={item.category || "General"}
                        owner_name={item.owner_name}
                        imageSrc={imageSrc}
                        type={type}
                        rating={item.average_rating || 0}
                        use_count={item.use_count || 0}
                        tags={item.tags && Array.isArray(item.tags) ? item.tags : []}
                        avatar={item.avatar || undefined}
                      />
                    );
                  })}
                </div>
              )}
              <div className="mt-4 pt-4 border-t">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            </div>
          )}
        </div>
      </section>
      <Footer />
    </div>
  );
}

export default function Home() {
  return (
    <Suspense fallback={<HomePageFallback />}>
      <HomeContent />
    </Suspense>
  );
}