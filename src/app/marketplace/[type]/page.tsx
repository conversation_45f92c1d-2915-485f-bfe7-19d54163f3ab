import { redirect } from "next/navigation";

export default async function MarketplaceTypePage({ params }: any) {
  const type = params.type;

  // Redirect to the new routes
  switch (type) {
    case 'agents':
      redirect('/agents');
      break;
    case 'mcps':
      redirect('/mcps');
      break;
    case 'workflows':
      redirect('/workflows');
      break;
    default:
      redirect('/agents'); // fallback to agents
  }
}
