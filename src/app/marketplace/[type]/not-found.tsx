'use client'
import Link from "next/link";
import { Footer } from "@/components/footer";
import { But<PERSON> } from "@/components/ui/button";

export default function MarketplaceTypeNotFound() {
  return (
    <>
      <main className="min-h-screen bg-background">
        <div className="container flex flex-col items-center justify-center px-4 py-16 md:px-6 md:py-24">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">404</h1>
          <h2 className="mt-4 text-xl font-semibold tracking-tight sm:text-2xl md:text-3xl">Marketplace Section Not Found</h2>
          <p className="mt-4 text-center text-muted-foreground">
            Sorry, we couldn&apos;t find the marketplace section you&apos;re looking for.
          </p>
          <div className="mt-8 flex gap-4">
            <Button asChild variant="outline">
              <Link href="/marketplace">Browse Marketplace</Link>
            </Button>
            <Button asChild>
              <Link href="/">Return Home</Link>
            </Button>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
