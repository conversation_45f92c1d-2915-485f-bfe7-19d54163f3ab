'use client';

import { redirect, useParams } from "next/navigation";

export default function LegacyDetailPage() {
  const params = useParams();
  const type = params.type as string;
  const id = params.id as string;

  // Map the old route to the new route
  if (type === 'agents') {
    redirect(`/marketplace/agent/${id}`);
  } else if (type === 'mcps') {
    redirect(`/marketplace/mcp/${id}`);
  } else if (type === 'workflows') {
    redirect(`/marketplace/workflow/${id}`);
  } else {
    // Fallback to agents if type is unknown
    redirect(`/marketplace/agent/${id}`);
  }
}
