'use client';

import { useParams } from "next/navigation";
import { WorkflowDetailPage } from "@/components/workflow-detail-page";
import { useWorkflow } from "@/hooks/use-workflows";
import { MarketplaceItem } from "@/data/marketplace-items";
import { Skeleton } from "@/components/ui/skeleton";
import { Footer } from "@/components/footer";
import { useAuth } from "@/hooks/use-auth";

export default function WorkflowDetailPageRoute() {
  const params = useParams();
  const id = params.id as string;
  const { user, isAuthenticated } = useAuth();

  // Use the workflow hook to fetch data with user ID if authenticated
  const { data, isLoading, error, refetch } = useWorkflow(id, isAuthenticated ? user?.id : undefined);

  // Loading state
  if (isLoading) {
    return (
      <>
        <div className="container py-12">
          <div className="max-w-3xl mx-auto">
            <div className="flex flex-col space-y-6">
              <Skeleton className="h-12 w-3/4" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-2/3" />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="md:col-span-2 space-y-4">
                  <Skeleton className="h-8 w-1/3" />
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-8 w-1/4" />
                  <Skeleton className="h-32 w-full" />
                </div>
                <div className="space-y-4">
                  <Skeleton className="h-8 w-2/3" />
                  <Skeleton className="h-40 w-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  // Error state
  if (error) {
    return (
      <>
        <div className="container py-12 text-center">
          <h1 className="text-2xl font-bold mb-4">Error Loading Workflow</h1>
          <p className="text-muted-foreground mb-6">
            There was an error loading the Workflow details. Please try again later.
          </p>
          <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-left overflow-auto">
            {error instanceof Error ? error.message : "Unknown error"}
          </pre>
        </div>
        <Footer />
      </>
    );
  }

  // If we have data, use it directly
  if (data) {
    // Create a MarketplaceItem directly from the API data
    const item: MarketplaceItem = {
      id: data.id,
      title: data.name,
      description: data.description,
      category: 'category' in data ? data.category || "General" : "General",
      owner_name: 'owner_name' in data && typeof data.owner_name === 'string' ? data.owner_name : "RUH AI",
      rating: data?.average_rating || 0,
      use_count: data.execution_count || data.use_count || 1000,
      imageSrc: "/assets/workflow.png",
      type: "WORKFLOW",
      // Pass the raw data for additional details
      rawData: data,
      // Set is_added based on the API response
      is_added: 'is_added' in data ? !!data.is_added : false,
    };

    return <WorkflowDetailPage item={item} relatedItems={[]} refetch={refetch} />;
  }

  // Default fallback if no data
  const defaultItem: MarketplaceItem = {
    id: id,
    title: "Workflow Details",
    description: "Information about this Workflow is currently unavailable.",
    category: "General",
    owner_name: "RUH AI",
    rating: 4.5,
    use_count: 1000,
    imageSrc: "/assets/workflow.png",
    type: "WORKFLOW",
    rawData: null,
  };

  return <WorkflowDetailPage item={defaultItem} relatedItems={[]} refetch={refetch} />;
}
