import { getRuhExecutionApiUrl } from '@/lib/helpers';
import generalAxiosClient from '../../general-axios-client';
import { ApiResponse } from '../types';
const BASE_URL = getRuhExecutionApiUrl();

// Types for MCP tool execution API
export interface MCPToolExecutionRequest {
  mcp_id: string;
  tool_name: string;
  tool_parameters: Record<string, any>;
}

export interface MCPToolExecutionResponse {
  success: boolean;
  message: string;
  request_id: string;
  result?: any;
}

/**
 * MCP Tools Service
 * Handles API calls for MCP tools execution
 */
export const mcpToolsService = {
  /**
   * Execute MCP tool
   * @param request MCPToolExecutionRequest
   * @returns Promise with MCPToolExecutionResponse
   */
  executeTool: async (request: MCPToolExecutionRequest): Promise<ApiResponse<MCPToolExecutionResponse>> => {
    try {
      // Use general axios client for non-marketplace endpoints
      const response = await generalAxiosClient.post(
        `${BASE_URL}/mcp-execute/server`,
        request
      );
      console.log(`Executing tool ${request.tool_name} for MCP ID ${request.mcp_id}:`, response);

      return {
        data: response.data,
        status: response.status,
        message: 'Tool executed successfully',
      };
    } catch (error: unknown) {
      console.error(`Error executing tool ${request.tool_name} for MCP ID ${request.mcp_id}:`, error);
      throw error;
    }
  }
};
