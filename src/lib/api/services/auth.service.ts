import axiosClient from '../axios-client';
import { mockAuthAPI } from '../mock-auth';
import { ApiResponse, AuthResponse, LoginRequest, RegisterRequest, User } from '../types';

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development';

const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  LOGOUT: '/auth/logout',
  ME: '/auth/me',
};

export const authService = {
  /**
   * Login user
   */
  login: async (data: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    // Use mock API in development
    if (isDev) {
      return mockAuthAPI.login(data);
    }

    const response = await axiosClient.post<ApiResponse<AuthResponse>>(AUTH_ENDPOINTS.LOGIN, data);
    return response.data;
  },

  /**
   * Register new user
   */
  register: async (data: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    // Use mock API in development
    if (isDev) {
      return mockAuthAPI.register(data);
    }

    const response = await axiosClient.post<ApiResponse<AuthResponse>>(AUTH_ENDPOINTS.REGISTER, data);
    return response.data;
  },

  /**
   * Logout user
   */
  logout: async (): Promise<ApiResponse<null>> => {
    // Use mock API in development
    if (isDev) {
      return mockAuthAPI.logout();
    }

    const response = await axiosClient.post<ApiResponse<null>>(AUTH_ENDPOINTS.LOGOUT);
    return response.data;
  },

  /**
   * Get current user
   */
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    // Use mock API in development
    if (isDev) {
      return mockAuthAPI.getCurrentUser();
    }

    const response = await axiosClient.get<ApiResponse<User>>(AUTH_ENDPOINTS.ME);
    return response.data;
  },
};
