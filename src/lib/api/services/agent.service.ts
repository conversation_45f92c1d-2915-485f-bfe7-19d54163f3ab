import axiosClient from '../axios-client';
import { ApiResponse } from '../types';
import {
  Agent,
  AgentResponse,
  MarketplaceFilterParams,
  ListAgentVersionsResponse,
  AgentVersionsFilterParams
} from '../types/marketplace-items';

/**
 * Agent Service
 * Handles API calls to the Agent templates endpoints
 */
export const agentService = {
  /**
   * Get all Agents with optional filtering
   * @param params Optional filter parameters
   * @returns Promise with LegacyAgentResponse (for backward compatibility)
   */
  getAgents: async (params?: MarketplaceFilterParams): Promise<ApiResponse<AgentResponse>> => {
    try {
      // Convert params to match the API's expected format
      const apiParams: Record<string, any> = {};

      if (params) {
        // Map the params to the API's expected format
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
        // Use page_size if available, otherwise use limit for backward compatibility
        else if (params.limit) apiParams.page_size = params.limit;

        if (params.search) apiParams.search = params.search;
        if (params.category) apiParams.category = params.category;
        if (params.tags) apiParams.tags = params.tags;

        if (params.visibility) apiParams.visibility = params.visibility;
        if (params.status) apiParams.status = params.status;
      }

      console.log('API params for /agents endpoint:', apiParams);

      const response = await axiosClient.get<AgentResponse>('/agents', { params: apiParams });
      console.log('Agent API Response:', response);

      // Return the actual API response without transformation
      return {
        data: response.data,
        status: response.status,
        message: 'Agents retrieved successfully',
      };
    } catch (error: unknown) {
      console.error('Error fetching Agents:', error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get a single Agent by ID
   * @param id Agent ID
   * @param userId Optional user ID to check if the item is already added
   * @returns Promise with Agent
   */
  getAgentById: async (id: string, userId?: string): Promise<ApiResponse<Agent>> => {
    try {
      // Add userId to params if provided
      const params = userId ? { user_id: userId } : undefined;
      const response = await axiosClient.get(`/agents/${id}`, { params });
      console.log(`Fetching Agent with ID ${id}:`, response);

      // Handle different response structures
      let agentData: Agent;

      if (response.data.template) {
        // Old response structure with template property
        agentData = response.data.template;
      } else if (response.data.data) {
        // New response structure with data property
        agentData = response.data.data;
      } else if (response.data.agent) {
        // Agent detail response structure
        agentData = response.data.agent;
      } else {
        // Assume the response is the agent itself
        agentData = response.data;
      }

      // Log the extracted agent data
      console.log(`Extracted agent data for ID ${id}:`, agentData);

      return {
        data: agentData,
        status: response.status,
        message: 'Agent retrieved successfully',
      };
    } catch (error: unknown) {
      console.error(`Error fetching Agent with ID ${id}:`, error);

      // Rethrow the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get all versions of an agent
   * @param agentId Agent ID
   * @param params Optional filter parameters
   * @returns Promise with ListAgentVersionsResponse
   */
  getAgentVersions: async (
    agentId: string,
    params?: AgentVersionsFilterParams
  ): Promise<ApiResponse<ListAgentVersionsResponse>> => {
    try {
      // Convert params to match the API's expected format
      const apiParams: Record<string, any> = {};

      if (params) {
        if (params.marketplace_listing_id)
          apiParams.marketplace_listing_id = params.marketplace_listing_id;
        if (params.page) apiParams.page = params.page;
        if (params.page_size) apiParams.page_size = params.page_size;
      }

      const response = await axiosClient.get<ListAgentVersionsResponse>(
        `/agents/${agentId}/versions`,
        { params: apiParams }
      );

      return {
        data: response.data,
        status: response.status,
        message:
          response.data.message || "Agent versions retrieved successfully",
      };
    } catch (error: unknown) {
      console.error(
        `Error fetching versions for Agent with ID ${agentId}:`,
        error
      );

      // Rethrow the error to be handled by the caller
      throw error;
    }
  }
};
