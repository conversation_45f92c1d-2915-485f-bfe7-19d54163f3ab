// Common response structure
export interface ItemsResponse<T> {
  templates: T[];
  total: number;
  page: number;
  total_pages: number;
}

// Unified response type
export interface UnifiedResponse<T> {
  data: T[];
  metadata: {
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
    next_page: number | null;
    prev_page: number | null;
  };
}

// Base MarketplaceItem type
export interface BaseMarketplaceItem {
  id: string;
  name: string;
  description: string;
  item_type: "MCP" | "AGENT" | "WORKFLOW";
  category: string | null;
  tags: string[] | null;
  created_at: string;
  updated_at: string;
  owner_id: string;
  owner_name: string;
  average_rating: number | null;
  visibility: string;
  department: string | null;
  version: string | null;
  sse_url: string | null;
  avatar: string | null;
  use_count?: number;
  rating?: number | null;
  downloads?: number | null;
  status?: string;
}

// MCP type
export interface MCP extends BaseMarketplaceItem {
  item_type: "MCP";
  git_url?: string;
  git_branch?: string;
  gcr_image?: string;
}

// Agent type
export interface Agent extends BaseMarketplaceItem {
  item_type: "AGENT";
  agent_category?: string;
  system_message?: string;
  model_provider?: string;
  model_name?: string;
  model_api_key?: string;
  workflow_ids?: string[];
  mcp_server_ids?: string[];
  agent_topic_type?: string;
  subscriptions?: string;
  tone?: string;
  files?: string[];
  urls?: string[];
}

// Workflow type
export interface Workflow extends BaseMarketplaceItem {
  item_type: "WORKFLOW";
  execution_count: number;
  image_url?: string | null;
  workflow_url?: string | null;
  builder_url?: string | null;
  start_nodes?: string | null;
  workflow_definition?: string | null;
  workflow_steps?: string | null;
  source_workflow_id?: string | null;
}

// MarketplaceItem type
export type MarketplaceItem = MCP | Agent | Workflow;

// Response types
export type MCPResponse = UnifiedResponse<MCP>;
export type AgentResponse = UnifiedResponse<Agent>;
export type WorkflowResponse = UnifiedResponse<Workflow>;
export type CombinedResponse = UnifiedResponse<MarketplaceItem>;

// Workflow Version types
export interface WorkflowVersionInDB {
  id: string;
  workflow_id: string;
  version_number: string;
  name?: string | null;
  description?: string | null;
  workflow_url?: string | null;
  builder_url?: string | null;
  start_nodes?: Record<string, any>[] | null;
  category?: string | null;
  tags?: string[] | null;
  changelog?: string | null;
  status?: string | null;
  is_customizable?: boolean | null;
  created_at?: string | null;
  is_current?: boolean | null;
}

export interface ListWorkflowVersionsResponse {
  success: boolean;
  message: string;
  versions: WorkflowVersionInDB[];
  total: number;
  page: number;
  total_pages: number;
  current_version_id?: string | null;
}

// Agent Version types
export interface AgentVersionInDB {
  id: string;
  agent_id: string;
  version_number: string;
  name?: string | null;
  description?: string | null;
  system_message?: string | null;
  model_provider?: string | null;
  model_name?: string | null;
  category?: string | null;
  tags?: string[] | null;
  changelog?: string | null;
  status?: string | null;
  created_at?: string | null;
  is_current?: boolean | null;
  version_notes?: string | null;
}

export interface ListAgentVersionsResponse {
  success: boolean;
  message: string;
  versions: AgentVersionInDB[];
  total: number;
  page: number;
  total_pages: number;
  current_version_id?: string | null;
}

export interface WorkflowDetailResponse {
  success: boolean;
  message: string;
  workflow: Workflow;
}


// Workflow versions filter parameters
export interface WorkflowVersionsFilterParams {
  marketplace_listing_id?: string | null;
  page?: number;
  page_size?: number;
}

// Agent versions filter parameters
export interface AgentVersionsFilterParams {
  marketplace_listing_id?: string | null;
  page?: number;
  page_size?: number;
}

// Filter parameters
export interface MarketplaceFilterParams {
  page?: number;
  page_size?: number;
  limit?: number; // Kept for backward compatibility
  category?: string;
  category_type?: string;
  visibility?: string;
  status?: string;
  search?: string;
  tags?: string;
}

// Legacy response types (for backward compatibility)
export type LegacyMCPResponse = ItemsResponse<MCP>;
export type LegacyAgentResponse = ItemsResponse<Agent>;
export type LegacyWorkflowResponse = ItemsResponse<Workflow>;
export type LegacyCombinedMarketplaceResponse = ItemsResponse<MarketplaceItem>;
