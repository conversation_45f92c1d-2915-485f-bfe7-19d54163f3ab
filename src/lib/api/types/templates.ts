// Common template response structure
export interface TemplateResponse<T> {
  templates: T[];
  total: number;
  page: number;
  total_pages: number;
}

// MCP Template types
export interface MCPTemplate {
  id: string;
  name: string;
  description: string;
  visibility: string;
  category: string;
  tags: Record<string, unknown>;
  status: string;
  git_url: string;
  git_branch: string;
  gcr_image: string;
  sse_url: string;
  created_at: string;
  updated_at: string;
}

// MCP template response type with specific MCPTemplate items
export type MCPTemplateResponse = TemplateResponse<MCPTemplate>;

// Agent Template types
export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  avatar: string;
  agent_category: string;
  system_message: string;
  model_provider: string;
  model_name: string;
  model_api_key: string;
  workflow_ids: string[];
  mcp_server_ids: string[];
  agent_topic_type: string;
  subscriptions: string;
  tags: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  department: string;
  tone: string;
  files: string[];
  urls: string[];
}

// Agent template response type with specific AgentTemplate items
export type AgentTemplateResponse = TemplateResponse<AgentTemplate>;

// Workflow Template types
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  workflow_url: string;
  builder_url: string;
  execution_count: number;
  visibility: string;
  category: string;
  tags: Record<string, unknown>;
  version: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface WorkflowTemplateResponse {
  success: boolean;
  templates: WorkflowTemplate[];
  total: number;
  page: number;
  total_pages: number;
}

// Template filter parameters
export interface TemplateFilterParams {
  page?: number;
  limit?: number;
  category?: string;
  visibility?: string;
  status?: string;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}
