// Common API response type
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

// Export marketplace item types
export * from './types/marketplace-items';

// Pagination response type
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Error response type
export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  passwordConfirmation: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// Marketplace item types (based on your existing types)
export type ItemType = "MCP" | "AGENT" | "WORKFLOW";

export interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  category: string;
  author: string;
  rating: number;
  downloads: number;
  imageSrc: string;
  type: ItemType;
  price?: number;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
}

// Filter and search types
export interface FilterOptions {
  type?: ItemType;
  category?: string;
  minRating?: number;
  maxPrice?: number;
  tags?: string[];
  sortBy?: 'rating' | 'downloads' | 'price' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchParams extends FilterOptions {
  query?: string;
  // page and limit are already in FilterOptions
}
