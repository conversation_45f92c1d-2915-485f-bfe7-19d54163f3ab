import { ApiResponse, AuthResponse, LoginRequest, RegisterRequest, User } from './types';

// Mock user data
const mockUsers: User[] = [
  {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock token
const mockToken = 'mock-jwt-token';

// Mock auth API
export const mockAuthAPI = {
  // Login
  login: (data: LoginRequest): ApiResponse<AuthResponse> => {
    const user = mockUsers.find(user => user.email === data.email);
    
    if (!user) {
      return {
        data: {} as AuthResponse,
        status: 401,
        message: 'Invalid credentials',
      };
    }
    
    return {
      data: {
        user,
        token: mockToken,
      },
      status: 200,
      message: 'Login successful',
    };
  },
  
  // Register
  register: (data: RegisterRequest): ApiResponse<AuthResponse> => {
    // Check if user already exists
    const existingUser = mockUsers.find(user => user.email === data.email);
    
    if (existingUser) {
      return {
        data: {} as AuthResponse,
        status: 400,
        message: 'User already exists',
      };
    }
    
    // Create new user
    const newUser: User = {
      id: (mockUsers.length + 1).toString(),
      name: data.name,
      email: data.email,
      role: 'user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to mock users
    mockUsers.push(newUser);
    
    return {
      data: {
        user: newUser,
        token: mockToken,
      },
      status: 201,
      message: 'Registration successful',
    };
  },
  
  // Logout
  logout: (): ApiResponse<null> => {
    return {
      data: null,
      status: 200,
      message: 'Logout successful',
    };
  },
  
  // Get current user
  getCurrentUser: (): ApiResponse<User> => {
    return {
      data: mockUsers[0],
      status: 200,
      message: 'User retrieved successfully',
    };
  },
};
