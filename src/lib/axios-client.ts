import axios from 'axios';
import { cookieUtils } from './cookie';

// Define base API URL from environment variable or use a default
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.ruh.ai';

// Create an Axios instance with default config
const axiosClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 50000, // 10 seconds
});

// Request interceptor for adding auth token
axiosClient.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const accessToken = cookieUtils.getAccessToken();
      const tokenType = cookieUtils.getTokenType() || 'Bearer';

      if (accessToken && config.headers) {
        config.headers.Authorization = `${tokenType} ${accessToken}`;
      }
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
axiosClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);

    // Handle unauthorized error (401)
    if (error.response?.status === 401) {
      console.error('Unauthorized access');

      // Clear auth tokens
      cookieUtils.removeAuthTokens();

      // Don't redirect automatically - let the components handle it
      // This prevents redirect loops and allows for better UX
    }

    return Promise.reject(error);
  }
);

export default axiosClient;
