import axiosClient from '@/lib/axios-client';

// User details type
export type UserDetails = {
  id: string;
  email: string;
  fullName: string;
  company?: string;
  department?: string;
  jobRole?: string;
  phoneNumber?: string;
  profileImage?: string;
  createdAt?: string;
  updatedAt?: string;
  isFirstLogin?: boolean;
};

// User service with methods for getting user data
export const userService = {
  /**
   * Get current user details
   */
  getCurrentUser: async (): Promise<UserDetails> => {
    try {
      // The API now returns the user details directly in the response body
      // without wrapping it in a data property
      const response = await axiosClient.get<UserDetails>('/users/me');

      // Return the response data directly as it matches our UserDetails type
      return response.data;
    } catch (error) {
      console.error('Error fetching user details:', error);
      throw error;
    }
  },
};
