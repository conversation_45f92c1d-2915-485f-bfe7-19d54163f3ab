import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define the routes that should be redirected
const redirectRoutes = [
  { from: '/mcps', to: '/marketplace/mcps' },
  { from: '/agents', to: '/marketplace/agents' },
  { from: '/workflows', to: '/marketplace/workflows' },
];

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the current path matches any of the old routes
  for (const route of redirectRoutes) {
    // Exact match for the main route
    if (pathname === route.from) {
      return NextResponse.redirect(new URL(route.to, request.url));
    }
    
    // Match for detail pages (e.g., /mcps/123 -> /marketplace/mcps/123)
    if (pathname.startsWith(`${route.from}/`)) {
      const newPath = pathname.replace(route.from, route.to);
      return NextResponse.redirect(new URL(newPath, request.url));
    }
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: ['/mcps/:path*', '/agents/:path*', '/workflows/:path*'],
};
